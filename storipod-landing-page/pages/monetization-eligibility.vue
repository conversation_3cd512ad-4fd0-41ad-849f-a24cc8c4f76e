<template>
    <div class="max-w-4xl px-4 py-10 mx-auto">
        <img src="@/assets/img/policies.jpg" class="w-20 h-20 rounded-lg" alt="Storipod logo">
        <h1 class="mb-2 text-4xl font-extrabold text-gray-900">Monetization & Creator Eligibility</h1>

        <div v-for="(section, index) in monetizationSection" :key="index" class="py-6 mb-3 border-b">
            <div v-if="section.description" class="mb-2 font-bold text-gray-800 text-md" v-html="section.description" />
            <div class="max-w-none" v-html="section.content" />
        </div>
    </div>
</template>

<script>
import { monetizationSection } from '../utils/originals';


export default {
    data() {
        return {
            monetizationSection
        }
    }
}
</script>

<style scoped>
ol.list-decimal {
  list-style-type: decimal;
  padding-left: 1.25rem; 
  margin-top: 0.25rem;
  margin-bottom: 1rem;
}
</style>
