<template>
    <div class="min-h-screen bg-white">
      <!-- <HeroAnimation /> -->
      <!-- Hero Section -->
      <section class="container mx-auto px-4 py-16 lg:py-24">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <div>
            <h1 class="text-8xl font-bold text-black leading-tight mb-8">
              It pays<br>
              to be here
            </h1>
          </div>
          <div class="p-8 rounded-2xl">
            <h2 class="text-2xl font-medium mb-6">Create, earn, trade, discover apps, and chat with friends all in one place.</h2>
            <div class="space-y-4">
              <input 
                v-model="email"
                type="email" 
                placeholder="Enter your email"
                class="w-full px-4 py-3.5 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
              <button class="bg-black text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-800 transition-colors">
                Join the waitlist
              </button>
              <p class="text-sm text-gray-500">
                Learn how we collect your information by visiting our <a href="#" class="underline">Privacy Policy</a>
              </p>
            </div>
          </div>
        </div>
      </section>
  
      <!-- One App Everything Connected Section -->
      <section class="bg-gray-50 py-16 lg:py-24">
        <div class="container mx-auto px-4">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
           <img src="@/assets/images/step1.webp" />
            
            <div>
              <h2 class="text-4xl lg:text-6xl font-bold text-black mb-6">
                One app, everything connected
              </h2>
              <p class="text-lg text-gray-600 mb-6">
                An app where creativity is rewarded. A place to post, earn, trade, play, and chat with friends.
              </p>
              <p class="text-lg text-gray-600">
                Just you, your network, and your money—working together.
              </p>
            </div>
          </div>
        </div>
      </section>
  
      <!-- Create and Earn Section -->
      <section class="py-16 lg:py-24">
        <div class="container mx-auto px-4">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-4xl lg:text-6xl font-bold text-black mb-6">
                Create<br>
                and earn
              </h2>
              <p class="text-lg text-gray-600">
                Get paid when people engage with your content.<sup>1</sup>
              </p>
            </div>
            <img src="@/assets/images/step2.webp" />
          </div>
        </div>
      </section>
  
      <!-- Make it Yours Section -->
      <section class="bg-gray-50 py-16 lg:py-24">
        <div class="container mx-auto px-4">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-4xl lg:text-6xl font-bold text-black mb-6">
                Make it yours
              </h2>
              <p class="text-lg text-gray-600">
                Set a theme to make the app truly yours.
              </p>
            </div>
            
            <img src="@/assets/images/step8.webp" />
          </div>
        </div>
      </section>
  
  
      <!-- Messaging Meets Money Section -->
      <section class="py-16 lg:py-24">
        <div class="container mx-auto px-4">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-4xl lg:text-6xl font-bold text-black mb-6">
                Messaging<br>
                meets money
              </h2>
              <p class="text-lg text-gray-600">
                Send money as instantly and freely as messages. DMs, group chats, and AI agents — all encrypted, private, and secure.
              </p>
            </div>
            
          <img src="@/assets/images/step6.webp" />
          </div>
        </div>
      </section>
  
      <!-- Earn More Section -->
      <section class="bg-gray-50 py-16 lg:py-24">
        <div class="container mx-auto px-4">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-4xl lg:text-6xl font-bold text-black mb-6">
                Earn more<br>
                from your cash
              </h2>
              <p class="text-lg text-gray-600">
                Get up to *4.1% APY in rewards by holding USDC in the app.
              </p>
            </div>
            
           <img src="@/assets/images/step7.webp" />
          </div>
        </div>
      </section>
  
      <!-- Social Network Section -->
      <section class="py-16 lg:py-24">
        <div class="container mx-auto px-4">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-4xl lg:text-6xl font-bold text-black mb-6">
                A new social<br>
                network
              </h2>
              <p class="text-lg text-gray-600">
                A feed to discover apps, coins, videos, communities, and more.
              </p>
            </div>
            
            <div class="bg-gray-50 p-8 rounded-2xl">
              <div class="bg-white rounded-2xl p-4 max-w-sm mx-auto">
                <div class="flex items-center justify-between mb-4">
                  <span class="text-sm">3:18</span>
                  <div class="flex space-x-1">
                    <div class="w-1 h-4 bg-black"></div>
                    <div class="w-1 h-4 bg-black"></div>
                    <div class="w-1 h-4 bg-black"></div>
                  </div>
                </div>
                
                <div class="flex items-center justify-between mb-6">
                  <div class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-black rounded-lg"></div>
                    <span class="font-medium">Social</span>
                  </div>
                  <div class="flex space-x-4">
                    <span>🔍</span>
                    <span>⚙️</span>
                  </div>
                </div>
                
                <div class="flex space-x-4 mb-6">
                  <span class="font-medium border-b-2 border-black pb-1">Discover</span>
                  <span class="text-gray-500">Videos</span>
                </div>
                
                <div class="space-y-4">
                  <div class="flex items-start space-x-3">
                    <div class="w-8 h-8 bg-gray-400 rounded-full"></div>
                    <div class="flex-1">
                      <p class="text-sm"><strong>Mike</strong> @yoitsmikey.base.eth • 5m</p>
                      <p class="text-sm">Need to find new music to work to. Everyone drop your recs!</p>
                      <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                        <span>💬 2</span>
                        <span>🔄 14</span>
                        <span>❤️ 45</span>
                        <span>↗️ 4,402</span>
                      </div>
                    </div>
                  </div>
                  
                  <div>
                    <p class="text-sm font-medium mb-2">Suggested videos</p>
                    <div class="grid grid-cols-3 gap-2">
                      <div class="aspect-video bg-yellow-600 rounded"></div>
                      <div class="aspect-video bg-purple-600 rounded"></div>
                      <div class="aspect-video bg-red-600 rounded"></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
  
      <!-- Trade Coins Section -->
      <section class="bg-gray-50 py-16 lg:py-24">
        <div class="container mx-auto px-4">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 class="text-4xl lg:text-6xl font-bold text-black mb-6">
                Trade millions<br>
                of coins
              </h2>
              <p class="text-lg text-gray-600">
                Buy, sell, and trade onchain assets.
              </p>
            </div>
            
          <img src="@/assets/images/step4.webp" />
          </div>
        </div>
      </section>

            <!-- Mini App Section -->
            <section class="py-16 lg:py-24">
        <div class="container mx-auto px-4 text-center">
          <div class="flex justify-center space-x-8 mb-8">
            <div class="w-16 h-16 bg-black rounded-2xl"></div>
            <div class="w-16 h-16 bg-black rounded-2xl"></div>
          </div>
          
          <h2 class="text-4xl lg:text-6xl font-bold text-black mb-6">
            There's a mini app for that
          </h2>
          <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Play games, earn yield, trade coins and more—all on Base. Discover apps made by builders all over the world. No downloads, no tab switching.
          </p>
          
          <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
            <button class="bg-black text-white py-3 px-8 rounded-lg font-medium">
              Discover on Base
            </button>
            <button class="border border-gray-300 text-black py-3 px-8 rounded-lg font-medium">
              Build a mini app
            </button>
          </div>
          
          <div class="flex justify-center space-x-8">
            <div class="text-right">
              <span class="text-2xl">NoteE</span>
            </div>
            <div class="w-16 h-16 bg-red-500 rounded-2xl"></div>
            <div class="w-16 h-16 bg-orange-400 rounded-2xl"></div>
          </div>
        </div>
      </section>
  

            <!-- Get on Waitlist Section -->
            <section class="bg-gray-50 py-16 lg:py-24">
        <div class="container mx-auto px-4">
          <div class="grid lg:grid-cols-2 gap-12 items-center">
            <img src="@/assets/images/storipod-hero.png" class="h-[800px] w-[400px]" />
            
            <div>
              <h2 class="text-4xl lg:text-6xl font-bold text-black mb-6">
                Get on<br>
                the waitlist
              </h2>
              <p class="text-lg text-gray-600 mb-8">
                The new experience is coming to all Base users soon.
              </p>
              
              <div class="space-y-4">
                <input 
                  v-model="waitlistEmail"
                  type="email" 
                  placeholder="Enter your email"
                  class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                <button class="w-full bg-black text-white py-3 px-6 rounded-lg font-medium hover:bg-gray-800 transition-colors">
                  Get early access
                </button>
                <p class="text-sm text-gray-500">
                  Learn how we collect your information by visiting our <a href="#" class="underline">Privacy Policy</a>
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref } from 'vue'
  
  // Reactive data
  const email = ref('')
  const waitlistEmail = ref('')
  
  // Meta tags for SEO
  // useHead({
  //   title: 'Base - It pays to be here',
  //   meta: [
  //     { name: 'description', content: 'Create, earn, trade, discover apps, and chat with friends all in one place.' },
  //     { name: 'viewport', content: 'width=device-width, initial-scale=1' }
  //   ]
  // })
  </script>
  
  <style scoped>
  /* Custom styles if needed */
  </style>