<template>
    <div class="max-w-4xl px-4 py-10 mx-auto">
        <img src="@/assets/img/policies.jpg" class="w-20 h-20 rounded-lg" alt="Storipod logo">
        <h1 class="mb-8 text-4xl font-extrabold text-gray-900">Storipod Originals</h1>

        <div v-for="(section, index) in policySection" :key="index" class="p-6 mb-3 border-b">
            <h2 class="mb-2 text-2xl font-bold text-gray-800">{{ section.title }}</h2>
            <div v-if="section.description" class="mb-2 font-bold text-gray-800 text-md" v-html="section.description"/>
            <div class="max-w-none" v-html="section.content" />
        </div>

        <div class="mt-12 text-base text-gray-700">
            <p class="mb-2 font-semibold">
                Thank you for contributing to Storipod and helping us build a space where
                exceptional stories thrive and writers are rewarded for their creativity and originality.
            </p>
            <p class="font-semibold">Happy Po<PERSON>,<br />The Storipod Team</p>
        </div>
    </div>
</template>

<script>
import { policySection } from '../utils/originals';


export default {
    data() {
        return {
            policySection
        }
    }
}
</script>

