<template>
  <section class="z-10 lg:pt-0">
    <section id="footer" class="relative z-10 hidden space-y-32 bg-black card lg:space-y-0 lg:block lg:z-0">
      <div class="px-10 pt-32 mx-auto lg:w-6/12">
        <div class="flex items-center justify-center py-0 my-0">
          <h1 data-aos="fade-down" class="py-0 my-0 font-extrabold text-white lg:text-9xl text-8xl cta_text_small">
            Tell
          </h1>
        </div>
        <div class="flex items-start justify-start py-0 my-0">
          <h1 data-aos="fade-down" class="py-0 my-0 font-extrabold text-white lg:text-9xl text-8xl cta_text_small">
            Your
          </h1>
        </div>
        <div class="static py-0 my-0 lg:relative">
          <h1 data-aos="fade-down"
            class="py-0 my-0 text-[#00A1C1] absolute lg:static left-6 font-extrabold lg:pr-30 lg:mr-40  lg:text-9xl text-8xl flex justify-end items-end pt-0 mt-0 cta_text_small">
            Stori
          </h1>
        </div>
      </div>

      <footer class="pb-10 text-white bg-black lg:pb-0">
        <div class="max-w-screen-xl px-4 pt-16 mx-auto sm:px-6 lg:px-8">
          <div class="grid grid-cols-2 gap-8 text-white lg:grid-cols-3">
            <div class="grid grid-cols-2 gap-8 sm:grid-cols-2 md:grid-cols-4 lg:col-span-2">

              <div class="sm:text-left">
                <p class="text-lg font-medium text-white">
                  Downloads
                </p>

                <ul class="mt-8 space-y-4 text-sm">
                  <li>
                    <a class="text-gray-500 transition" href="https://apps.apple.com/ng/app/storipod/id6463716901">
                      iOS App
                    </a>
                  </li>
                  <li>
                    <a class="text-gray-500 transition"
                      href="https://play.google.com/store/apps/details?id=com.app.storipod_app">
                      Android App
                    </a>
                  </li>
                  <li>
                    <a class="text-gray-500 transition" href="https://storipod.app/#reserve-username">
                      Reserve Username
                    </a>
                  </li>
                </ul>
              </div>

              <div class="sm:text-left">
                <p class="text-lg font-medium text-white">
                  Policies
                </p>

                <ul class="mt-8 space-y-4 text-sm">
                  <li>
                    <nuxt-link class="text-gray-500 transition" to="/rules">
                      End User Agreement
                    </nuxt-link>
                  </li>
                  <li>
                    <nuxt-link class="text-gray-500 transition" to="/terms">
                      Terms of use
                    </nuxt-link>
                  </li>
                  <li>
                    <nuxt-link class="text-gray-500 transition" to="/privacy">
                      Privacy Policy
                    </nuxt-link>
                  </li>
                  <li>
                    <nuxt-link class="text-gray-500 transition" to="/originals-policy">
                      Storipod Originals Policy
                    </nuxt-link>
                  </li>
                  <li>
                    <nuxt-link class="text-gray-500 transition" to="/monetization-eligibility">
                      Monetization & Creators Eligibility
                    </nuxt-link>
                  </li>
                </ul>
              </div>

              <div class="pr-6 sm:text-left">
                <p class="text-lg font-medium text-white">
                  Contact
                </p>

                <ul class="mt-8 space-y-4 text-sm">
                  <li>
                    <a class="text-gray-500 transition" target="_blank" href="https://storipod.framer.website/">About
                      Us</a>
                  </li>
                  <li>
                    <nuxt-link class="text-gray-500 transition" to="/faqs">FAQ</nuxt-link>
                  </li>
                  <li>
                    <a class="text-gray-500 transition" href="https://www.instagram.com/storipodapp/">Instagram</a>
                  </li>
                  <li>
                    <a class="text-gray-500 transition" href="https://x.com/storipod_app">Twitter</a>
                  </li>
                  <li>
                    <a class="text-gray-500 transition" href="https://www.facebook.com/storipod/">Facebook</a>
                  </li>
                  <li>
                    <a class="text-gray-500 transition" href="https://www.linkedin.com/company/stori-pod/">Linkedin</a>
                  </li>
                  <li>
                    <a class="text-gray-500 transition" href="mailto:<EMAIL>">Email</a>
                  </li>
                </ul>
              </div>
            </div>
            <div class="flex flex-col items-start justify-start">
              <div class="flex justify-center text-teal-600 sm:justify-start">
                <svg width="135" height="30" viewBox="0 0 135 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M9.72 23.512C7.88533 23.512 6.11467 23.2347 4.408 22.68C2.72267 22.1253 1.26133 21.3467 0.024 20.344L2.744 16.536C4.728 18.0933 7.04267 18.872 9.688 18.872C11.0107 18.872 12.056 18.648 12.824 18.2C13.6133 17.752 14.008 17.144 14.008 16.376C14.008 15.6507 13.6453 15.0853 12.92 14.68C12.1947 14.2747 11.0427 13.9867 9.464 13.816C6.41333 13.4747 4.17333 12.76 2.744 11.672C1.336 10.584 0.632 9.02667 0.632 7C0.632 5.63467 1.016 4.42933 1.784 3.384C2.552 2.33867 3.62933 1.528 5.016 0.952C6.40267 0.375999 8.00267 0.0879993 9.816 0.0879993C11.3733 0.0879993 12.888 0.311999 14.36 0.759998C15.8533 1.208 17.1653 1.83733 18.296 2.648L15.832 6.328C14.0827 5.112 12.152 4.504 10.04 4.504C8.73867 4.504 7.69333 4.71733 6.904 5.144C6.136 5.57067 5.752 6.14667 5.752 6.872C5.752 7.576 6.08267 8.12 6.744 8.504C7.42667 8.888 8.55733 9.176 10.136 9.368C13.1653 9.70933 15.4267 10.4667 16.92 11.64C18.4133 12.792 19.16 14.3813 19.16 16.408C19.16 17.816 18.7653 19.064 17.976 20.152C17.1867 21.2187 16.0773 22.0507 14.648 22.648C13.2187 23.224 11.576 23.512 9.72 23.512ZM32.9035 22.392C31.6662 23.0107 30.2688 23.32 28.7115 23.32C26.9835 23.32 25.6182 22.84 24.6155 21.88C23.6128 20.8987 23.1115 19.3307 23.1115 17.176V11.192H20.4235V7.192H23.1115V3.256L28.1035 2.712V7.192H32.0395V11.192H28.1035V17.176C28.1035 17.816 28.2635 18.296 28.5835 18.616C28.9035 18.9147 29.3408 19.064 29.8955 19.064C30.5142 19.064 31.1222 18.9147 31.7195 18.616L32.9035 22.392ZM42.8203 23.352C41.0709 23.352 39.5349 23.0107 38.2123 22.328C36.9109 21.624 35.9083 20.6533 35.2043 19.416C34.5003 18.1787 34.1483 16.7387 34.1483 15.096C34.1483 13.4533 34.5003 12.0133 35.2043 10.776C35.9083 9.53867 36.9109 8.57867 38.2123 7.896C39.5349 7.192 41.0709 6.84 42.8203 6.84C44.5696 6.84 46.1056 7.192 47.4283 7.896C48.7509 8.57867 49.7643 9.53867 50.4683 10.776C51.1723 12.0133 51.5243 13.4533 51.5243 15.096C51.5243 16.7387 51.1723 18.1787 50.4683 19.416C49.7643 20.6533 48.7509 21.624 47.4283 22.328C46.1056 23.0107 44.5696 23.352 42.8203 23.352ZM42.8203 19.096C43.9296 19.096 44.8256 18.7227 45.5083 17.976C46.1909 17.2293 46.5323 16.2693 46.5323 15.096C46.5323 13.9227 46.1909 12.9627 45.5083 12.216C44.8256 11.4693 43.9296 11.096 42.8203 11.096C41.7323 11.096 40.8469 11.4693 40.1643 12.216C39.4816 12.9627 39.1403 13.9227 39.1403 15.096C39.1403 16.2693 39.4816 17.2293 40.1643 17.976C40.8469 18.7227 41.7323 19.096 42.8203 19.096ZM63.4113 6.84C64.1366 6.84 64.8619 6.97867 65.5873 7.256C66.3126 7.512 66.9419 7.896 67.4753 8.408L65.3633 12.088C64.9153 11.8107 64.4993 11.608 64.1153 11.48C63.7313 11.352 63.3259 11.288 62.8993 11.288C61.8326 11.288 60.9899 11.6293 60.3713 12.312C59.7526 12.9947 59.4433 13.9973 59.4433 15.32V23H54.4193V7.192H58.8033L59.1873 9.176C60.2113 7.61867 61.6193 6.84 63.4113 6.84ZM71.6973 5.624C70.8226 5.624 70.1079 5.368 69.5533 4.856C69.0199 4.344 68.7533 3.69333 68.7533 2.904C68.7533 2.09333 69.0306 1.432 69.5853 0.919998C70.1399 0.407998 70.8439 0.151999 71.6973 0.151999C72.5506 0.151999 73.2546 0.407998 73.8093 0.919998C74.3639 1.432 74.6413 2.09333 74.6413 2.904C74.6413 3.69333 74.3639 4.344 73.8093 4.856C73.2759 5.368 72.5719 5.624 71.6973 5.624ZM69.1693 7.192H74.1933V23H69.1693V7.192ZM87.9018 6.872C89.3738 6.872 90.6858 7.224 91.8378 7.928C92.9898 8.61067 93.8858 9.58133 94.5258 10.84C95.1658 12.0773 95.4858 13.496 95.4858 15.096C95.4858 16.696 95.1658 18.1253 94.5258 19.384C93.8858 20.6213 92.9898 21.592 91.8378 22.296C90.6858 22.9787 89.3738 23.32 87.9018 23.32C85.9391 23.32 84.3071 22.7013 83.0058 21.464V29.848H77.9818V7.192H82.3338L82.6538 9.048C83.9764 7.59733 85.7258 6.872 87.9018 6.872ZM86.7178 19.096C87.8271 19.096 88.7338 18.7333 89.4378 18.008C90.1418 17.2613 90.4938 16.2907 90.4938 15.096C90.4938 13.9013 90.1418 12.9413 89.4378 12.216C88.7338 11.4693 87.8271 11.096 86.7178 11.096C85.6084 11.096 84.7018 11.4693 83.9978 12.216C83.3151 12.9627 82.9738 13.9227 82.9738 15.096C82.9738 16.2693 83.3151 17.2293 83.9978 17.976C84.7018 18.7227 85.6084 19.096 86.7178 19.096ZM106.102 23.352C104.352 23.352 102.816 23.0107 101.494 22.328C100.192 21.624 99.1895 20.6533 98.4855 19.416C97.7815 18.1787 97.4295 16.7387 97.4295 15.096C97.4295 13.4533 97.7815 12.0133 98.4855 10.776C99.1895 9.53867 100.192 8.57867 101.494 7.896C102.816 7.192 104.352 6.84 106.102 6.84C107.851 6.84 109.387 7.192 110.71 7.896C112.032 8.57867 113.046 9.53867 113.75 10.776C114.454 12.0133 114.806 13.4533 114.806 15.096C114.806 16.7387 114.454 18.1787 113.75 19.416C113.046 20.6533 112.032 21.624 110.71 22.328C109.387 23.0107 107.851 23.352 106.102 23.352ZM106.102 19.096C107.211 19.096 108.107 18.7227 108.79 17.976C109.472 17.2293 109.814 16.2693 109.814 15.096C109.814 13.9227 109.472 12.9627 108.79 12.216C108.107 11.4693 107.211 11.096 106.102 11.096C105.014 11.096 104.128 11.4693 103.446 12.216C102.763 12.9627 102.422 13.9227 102.422 15.096C102.422 16.2693 102.763 17.2293 103.446 17.976C104.128 18.7227 105.014 19.096 106.102 19.096ZM134.277 0.599998V23H129.925L129.605 21.112C128.261 22.584 126.511 23.32 124.357 23.32C122.885 23.32 121.573 22.9787 120.421 22.296C119.29 21.592 118.405 20.6213 117.765 19.384C117.125 18.1253 116.805 16.696 116.805 15.096C116.805 13.496 117.125 12.0773 117.765 10.84C118.405 9.58133 119.29 8.61067 120.421 7.928C121.573 7.224 122.885 6.872 124.357 6.872C126.298 6.872 127.93 7.48 129.253 8.696V0.599998H134.277ZM125.541 19.096C126.65 19.096 127.557 18.7333 128.261 18.008C128.965 17.2613 129.317 16.2907 129.317 15.096C129.317 13.9013 128.965 12.9413 128.261 12.216C127.557 11.4693 126.65 11.096 125.541 11.096C124.431 11.096 123.525 11.4693 122.821 12.216C122.138 12.9627 121.797 13.9227 121.797 15.096C121.797 16.2693 122.138 17.2293 122.821 17.976C123.525 18.7227 124.431 19.096 125.541 19.096Z"
                    fill="white" />
                </svg>
              </div>

              <p class="max-w-md mt-6 leading-relaxed text-center text-gray-600 sm:max-w-xs sm:text-left">
                @ {{ new Date().getFullYear() }} Storipod
              </p>
            </div>
          </div>
        </div>
      </footer>
      <img src="@/assets/images/yellow-black.png" alt="" class="absolute right-0 top-28 h-60">
    </section>

    <section id="footer" class="relative z-10 space-y-32 bg-black card lg:space-y-0 lg:hidden lg:z-0">
      <div class="flex flex-col items-center justify-center px-10 pt-32 mx-auto lg:w-6/12">
        <div class="flex items-center justify-center py-0 my-0">
          <h1 data-aos="fade-down" class="py-0 my-0 font-extrabold text-white lg:text-9xl text-8xl cta_text_small">
            Tell
          </h1>
        </div>
        <div class="">
          <h1 data-aos="fade-down" class="py-0 my-0 font-extrabold text-white lg:text-9xl text-8xl cta_text_small">
            Your
          </h1>
        </div>
        <div class="">
          <h1
            class="py-0 my-0 text-[#00A1C1] absolute lg:static left-6 font-extrabold lg:pr-30 lg:mr-40  lg:text-9xl text-8xl flex justify-end items-end pt-0 mt-0 cta_text_small">
            Stori
          </h1>
        </div>
      </div>

      <img src="@/assets/images/yellow-black.png" alt="" class="absolute top-0 right-0 h-32">

      <footer class="relative pb-10 space-y-10 text-white bg-black lg:pb-0">
        <div class="max-w-screen-lg px-4 pt-16 mx-auto sm:px-6 lg:px-8">
          <div class="grid grid-cols-1 gap-8 text-white lg:grid-cols-3">
            <div class="grid grid-cols-1 gap-8 space-y-6 sm:grid-cols-1 md:grid-cols-4 lg:col-span-2">
              <div class="flex justify-between">
                <div class="sm:text-left">
                  <p class="text-lg font-medium text-white">
                    Product
                  </p>

                  <ul class="mt-2 space-y-4 text-sm">
                    <li>
                      <a class="text-gray-500 transition hover:" href="/">
                        Monetization
                      </a>
                    </li>

                    <li>
                      <a class="text-gray-500 transition hover:" href="/">
                        Newsletters
                      </a>
                    </li>

                    <li>
                      <a class="text-gray-500 transition hover:" href="/">
                        Reserve username
                      </a>
                    </li>

                    <li>
                      <a class="text-gray-500 transition hover:" href="/">
                        Help Center
                      </a>
                    </li>
                  </ul>
                </div>

                <div class="sm:text-left">
                  <p class="text-lg font-medium text-white">
                    Downloads
                  </p>

                  <ul class="mt-2 space-y-4 text-sm">
                    <li>
                      <a href="https://apps.apple.com/ng/app/storipod/id6463716901" class="text-gray-500 transition">
                        IOS App
                      </a>
                    </li>

                    <li>
                      <a href="https://play.google.com/store/apps/details?id=com.app.storipod_app"
                        class="text-gray-500 transition">
                        Android Apk
                      </a>
                    </li>
                  </ul>
                </div>
              </div>

              <div class="flex justify-between">
                <div class="sm:text-left">
                  <p class="text-lg font-medium text-white">
                    Policies
                  </p>

                  <ul class="mt-2 space-y-4 text-sm">
                    <li>
                      <nuxt-link class="text-gray-500 transition" to="/rules">
                        End User Agreement
                      </nuxt-link>
                    </li>

                    <li>
                      <nuxt-link class="text-gray-500 transition" to="/privacy">
                        Privacy Policy
                      </nuxt-link>
                    </li>
                    <li>
                      <nuxt-link class="text-gray-500 transition" to="/terms">
                        Terms of use
                      </nuxt-link>
                    </li>
                    <li>
                      <nuxt-link class="text-gray-500 transition" to="/originals-policy">
                        Storipod Originals Policy
                      </nuxt-link>
                    </li>
                    <li>
                      <nuxt-link class="text-gray-500 transition" to="/monetization-eligibility">
                        Monetization & Creators Eligibility
                      </nuxt-link>
                    </li>
                  </ul>
                </div>

                <div class="sm:text-left">
                  <p class="pr-8 text-lg font-medium text-white">
                    Contact
                  </p>

                  <ul class="mt-2 space-y-4 text-sm">
                    <li>
                      <a class="text-gray-500 transition" target="_blank" href="https://storipod.framer.website/">About
                        Us</a>
                    </li>

                    <li>
                      <a class="text-gray-500 transition" href="https://www.instagram.com/storipodapp/">Instagram</a>
                    </li>

                    <li>
                      <a class="text-gray-500 transition" href="https://x.com/storipod_app">Twitter</a>
                    </li>
                    <li>
                      <a class="text-gray-500 transition" href="https://www.linkedin.com/company/stori-pod/">Linkedin</a>
                    </li>

                    <li>
                      <a class="text-gray-500 transition" href="mailto:<EMAIL>">Email Us</a>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="max-w-screen-lg px-4 mx-auto">
          <div class="text-teal-600">
            <svg width="135" height="30" viewBox="0 0 135 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M9.72 23.512C7.88533 23.512 6.11467 23.2347 4.408 22.68C2.72267 22.1253 1.26133 21.3467 0.024 20.344L2.744 16.536C4.728 18.0933 7.04267 18.872 9.688 18.872C11.0107 18.872 12.056 18.648 12.824 18.2C13.6133 17.752 14.008 17.144 14.008 16.376C14.008 15.6507 13.6453 15.0853 12.92 14.68C12.1947 14.2747 11.0427 13.9867 9.464 13.816C6.41333 13.4747 4.17333 12.76 2.744 11.672C1.336 10.584 0.632 9.02667 0.632 7C0.632 5.63467 1.016 4.42933 1.784 3.384C2.552 2.33867 3.62933 1.528 5.016 0.952C6.40267 0.375999 8.00267 0.0879993 9.816 0.0879993C11.3733 0.0879993 12.888 0.311999 14.36 0.759998C15.8533 1.208 17.1653 1.83733 18.296 2.648L15.832 6.328C14.0827 5.112 12.152 4.504 10.04 4.504C8.73867 4.504 7.69333 4.71733 6.904 5.144C6.136 5.57067 5.752 6.14667 5.752 6.872C5.752 7.576 6.08267 8.12 6.744 8.504C7.42667 8.888 8.55733 9.176 10.136 9.368C13.1653 9.70933 15.4267 10.4667 16.92 11.64C18.4133 12.792 19.16 14.3813 19.16 16.408C19.16 17.816 18.7653 19.064 17.976 20.152C17.1867 21.2187 16.0773 22.0507 14.648 22.648C13.2187 23.224 11.576 23.512 9.72 23.512ZM32.9035 22.392C31.6662 23.0107 30.2688 23.32 28.7115 23.32C26.9835 23.32 25.6182 22.84 24.6155 21.88C23.6128 20.8987 23.1115 19.3307 23.1115 17.176V11.192H20.4235V7.192H23.1115V3.256L28.1035 2.712V7.192H32.0395V11.192H28.1035V17.176C28.1035 17.816 28.2635 18.296 28.5835 18.616C28.9035 18.9147 29.3408 19.064 29.8955 19.064C30.5142 19.064 31.1222 18.9147 31.7195 18.616L32.9035 22.392ZM42.8203 23.352C41.0709 23.352 39.5349 23.0107 38.2123 22.328C36.9109 21.624 35.9083 20.6533 35.2043 19.416C34.5003 18.1787 34.1483 16.7387 34.1483 15.096C34.1483 13.4533 34.5003 12.0133 35.2043 10.776C35.9083 9.53867 36.9109 8.57867 38.2123 7.896C39.5349 7.192 41.0709 6.84 42.8203 6.84C44.5696 6.84 46.1056 7.192 47.4283 7.896C48.7509 8.57867 49.7643 9.53867 50.4683 10.776C51.1723 12.0133 51.5243 13.4533 51.5243 15.096C51.5243 16.7387 51.1723 18.1787 50.4683 19.416C49.7643 20.6533 48.7509 21.624 47.4283 22.328C46.1056 23.0107 44.5696 23.352 42.8203 23.352ZM42.8203 19.096C43.9296 19.096 44.8256 18.7227 45.5083 17.976C46.1909 17.2293 46.5323 16.2693 46.5323 15.096C46.5323 13.9227 46.1909 12.9627 45.5083 12.216C44.8256 11.4693 43.9296 11.096 42.8203 11.096C41.7323 11.096 40.8469 11.4693 40.1643 12.216C39.4816 12.9627 39.1403 13.9227 39.1403 15.096C39.1403 16.2693 39.4816 17.2293 40.1643 17.976C40.8469 18.7227 41.7323 19.096 42.8203 19.096ZM63.4113 6.84C64.1366 6.84 64.8619 6.97867 65.5873 7.256C66.3126 7.512 66.9419 7.896 67.4753 8.408L65.3633 12.088C64.9153 11.8107 64.4993 11.608 64.1153 11.48C63.7313 11.352 63.3259 11.288 62.8993 11.288C61.8326 11.288 60.9899 11.6293 60.3713 12.312C59.7526 12.9947 59.4433 13.9973 59.4433 15.32V23H54.4193V7.192H58.8033L59.1873 9.176C60.2113 7.61867 61.6193 6.84 63.4113 6.84ZM71.6973 5.624C70.8226 5.624 70.1079 5.368 69.5533 4.856C69.0199 4.344 68.7533 3.69333 68.7533 2.904C68.7533 2.09333 69.0306 1.432 69.5853 0.919998C70.1399 0.407998 70.8439 0.151999 71.6973 0.151999C72.5506 0.151999 73.2546 0.407998 73.8093 0.919998C74.3639 1.432 74.6413 2.09333 74.6413 2.904C74.6413 3.69333 74.3639 4.344 73.8093 4.856C73.2759 5.368 72.5719 5.624 71.6973 5.624ZM69.1693 7.192H74.1933V23H69.1693V7.192ZM87.9018 6.872C89.3738 6.872 90.6858 7.224 91.8378 7.928C92.9898 8.61067 93.8858 9.58133 94.5258 10.84C95.1658 12.0773 95.4858 13.496 95.4858 15.096C95.4858 16.696 95.1658 18.1253 94.5258 19.384C93.8858 20.6213 92.9898 21.592 91.8378 22.296C90.6858 22.9787 89.3738 23.32 87.9018 23.32C85.9391 23.32 84.3071 22.7013 83.0058 21.464V29.848H77.9818V7.192H82.3338L82.6538 9.048C83.9764 7.59733 85.7258 6.872 87.9018 6.872ZM86.7178 19.096C87.8271 19.096 88.7338 18.7333 89.4378 18.008C90.1418 17.2613 90.4938 16.2907 90.4938 15.096C90.4938 13.9013 90.1418 12.9413 89.4378 12.216C88.7338 11.4693 87.8271 11.096 86.7178 11.096C85.6084 11.096 84.7018 11.4693 83.9978 12.216C83.3151 12.9627 82.9738 13.9227 82.9738 15.096C82.9738 16.2693 83.3151 17.2293 83.9978 17.976C84.7018 18.7227 85.6084 19.096 86.7178 19.096ZM106.102 23.352C104.352 23.352 102.816 23.0107 101.494 22.328C100.192 21.624 99.1895 20.6533 98.4855 19.416C97.7815 18.1787 97.4295 16.7387 97.4295 15.096C97.4295 13.4533 97.7815 12.0133 98.4855 10.776C99.1895 9.53867 100.192 8.57867 101.494 7.896C102.816 7.192 104.352 6.84 106.102 6.84C107.851 6.84 109.387 7.192 110.71 7.896C112.032 8.57867 113.046 9.53867 113.75 10.776C114.454 12.0133 114.806 13.4533 114.806 15.096C114.806 16.7387 114.454 18.1787 113.75 19.416C113.046 20.6533 112.032 21.624 110.71 22.328C109.387 23.0107 107.851 23.352 106.102 23.352ZM106.102 19.096C107.211 19.096 108.107 18.7227 108.79 17.976C109.472 17.2293 109.814 16.2693 109.814 15.096C109.814 13.9227 109.472 12.9627 108.79 12.216C108.107 11.4693 107.211 11.096 106.102 11.096C105.014 11.096 104.128 11.4693 103.446 12.216C102.763 12.9627 102.422 13.9227 102.422 15.096C102.422 16.2693 102.763 17.2293 103.446 17.976C104.128 18.7227 105.014 19.096 106.102 19.096ZM134.277 0.599998V23H129.925L129.605 21.112C128.261 22.584 126.511 23.32 124.357 23.32C122.885 23.32 121.573 22.9787 120.421 22.296C119.29 21.592 118.405 20.6213 117.765 19.384C117.125 18.1253 116.805 16.696 116.805 15.096C116.805 13.496 117.125 12.0773 117.765 10.84C118.405 9.58133 119.29 8.61067 120.421 7.928C121.573 7.224 122.885 6.872 124.357 6.872C126.298 6.872 127.93 7.48 129.253 8.696V0.599998H134.277ZM125.541 19.096C126.65 19.096 127.557 18.7333 128.261 18.008C128.965 17.2613 129.317 16.2907 129.317 15.096C129.317 13.9013 128.965 12.9413 128.261 12.216C127.557 11.4693 126.65 11.096 125.541 11.096C124.431 11.096 123.525 11.4693 122.821 12.216C122.138 12.9627 121.797 13.9227 121.797 15.096C121.797 16.2693 122.138 17.2293 122.821 17.976C123.525 18.7227 124.431 19.096 125.541 19.096Z"
                fill="white" />
            </svg>
          </div>

          <p class="max-w-md mt-6 leading-relaxed text-gray-600 sm:max-w-xs sm:text-left">
            @ 2023 Storipod
          </p>
        </div>
      </footer>
    </section>
  </section>
</template>

<script>
export default {

}
</script>

<style scoped>
.card {
  height: 100vh;
  width: 100vw;
  position: sticky;
  top: 0;
}

.custom_image {
  background: url("@/assets/images/custom.jpeg");
}

.hero {
  font-family: Helvetica Neue;
}

.HalfCard {
  height: 500px;
  width: 100vw;
  position: sticky;
  top: 0;
}
</style>
