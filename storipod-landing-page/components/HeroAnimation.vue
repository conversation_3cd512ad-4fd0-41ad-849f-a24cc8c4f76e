<template>
    <div class="min-h-screen bg-white overflow-hidden">
      <div class="relative w-full h-screen flex items-center">
        <!-- Animated dotted text container -->
        <div class="animated-text-container">
          <!-- First line of text -->
          <div class="text-line" style="animation-delay: 0s">
            <div class="dotted-text" style="animation-delay: 0s">
              <DottedWord text="CREATIVE" />
              <DottedWord text="DESIGN" />
              <DottedWord text="STUDIO" />
            </div>
          </div>
          
          <!-- Second line with image -->
          <div class="text-line flex items-center justify-center" style="animation-delay: 2s">
            <div class="dotted-text" style="animation-delay: 2s">
              <DottedWord text="INNOVATIVE" />
            </div>
            
            <!-- Profile image -->
            <div class="profile-image mx-8 flex-shrink-0">
              <img 
                src="/placeholder.svg?height=120&width=120"
                alt="Profile"
                class="w-20 h-20 md:w-24 md:h-24 lg:w-28 lg:h-28 rounded-lg object-cover shadow-lg"
              />
            </div>
            
            <div class="dotted-text" style="animation-delay: 4s">
              <DottedWord text="SOLUTIONS" />
            </div>
          </div>
          
          <!-- Third line -->
          <div class="text-line" style="animation-delay: 4s">
            <div class="dotted-text" style="animation-delay: 6s">
              <DottedWord text="FOR" />
              <DottedWord text="MODERN" />
              <DottedWord text="BRANDS" />
            </div>
          </div>
          
          <!-- Fourth line -->
          <div class="text-line" style="animation-delay: 6s">
            <div class="dotted-text" style="animation-delay: 8s">
              <DottedWord text="DIGITAL" />
              <DottedWord text="EXPERIENCES" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, defineComponent, h } from 'vue'
  
  // Component for individual dotted word
  const DottedWord = defineComponent({
    props: {
      text: String
    },
    setup(props) {
      const getDotPattern = (char) => {
        const patterns = {
          'A': [
            [0, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1]
          ],
          'B': [
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 0]
          ],
          'C': [
            [0, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 1],
            [0, 1, 1, 1, 0]
          ],
          'D': [
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 0]
          ],
          'E': [
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 1]
          ],
          'F': [
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0]
          ],
          'G': [
            [0, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 0],
            [1, 0, 1, 1, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 1, 1, 0]
          ],
          'H': [
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1]
          ],
          'I': [
            [1, 1, 1, 1, 1],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [1, 1, 1, 1, 1]
          ],
          'L': [
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 1, 1, 1, 1]
          ],
          'M': [
            [1, 0, 0, 0, 1],
            [1, 1, 0, 1, 1],
            [1, 0, 1, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1]
          ],
          'N': [
            [1, 0, 0, 0, 1],
            [1, 1, 0, 0, 1],
            [1, 0, 1, 0, 1],
            [1, 0, 0, 1, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1]
          ],
          'O': [
            [0, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 1, 1, 0]
          ],
          'P': [
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0],
            [1, 0, 0, 0, 0]
          ],
          'R': [
            [1, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 1, 1, 1, 0],
            [1, 0, 1, 0, 0],
            [1, 0, 0, 1, 0],
            [1, 0, 0, 0, 1]
          ],
          'S': [
            [0, 1, 1, 1, 0],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 0],
            [0, 1, 1, 1, 0],
            [0, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 1, 1, 0]
          ],
          'T': [
            [1, 1, 1, 1, 1],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0]
          ],
          'U': [
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 1, 1, 0]
          ],
          'V': [
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 0, 1, 0],
            [0, 1, 0, 1, 0],
            [0, 0, 1, 0, 0]
          ],
          'W': [
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [1, 0, 1, 0, 1],
            [1, 0, 1, 0, 1],
            [1, 1, 0, 1, 1],
            [1, 0, 0, 0, 1]
          ],
          'Y': [
            [1, 0, 0, 0, 1],
            [1, 0, 0, 0, 1],
            [0, 1, 0, 1, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0],
            [0, 0, 1, 0, 0]
          ]
        }
        
        return patterns[char] || patterns['A']
      }
  
      return () => h('div', {
        class: 'dot-word inline-block mx-2 md:mx-4'
      }, props.text.split('').map((char, charIndex) => {
        if (char === ' ') {
          return h('div', { class: 'inline-block w-4 md:w-6' })
        }
        
        const pattern = getDotPattern(char)
        
        return h('div', {
          class: 'dot-character inline-block mx-1 md:mx-2',
          key: charIndex
        }, pattern.map((row, rowIndex) => 
          h('div', { 
            class: 'dot-row flex justify-center',
            key: rowIndex
          }, row.map((isDot, colIndex) => 
            h('div', {
              class: `dot ${isDot ? 'active' : 'inactive'}`,
              key: `${rowIndex}-${colIndex}`
            })
          ))
        ))
      }))
    }
  })
  </script>
  
  <style scoped>
  .animated-text-container {
    @apply w-full px-4 md:px-8 lg:px-16;
    animation: slideIn 1s ease-out;
  }
  
  .text-line {
    @apply w-full flex flex-wrap items-center justify-center md:justify-start mb-4 md:mb-6;
    animation: fadeInSlide 2s ease-out both;
  }
  
  .dotted-text {
    @apply flex flex-wrap items-center justify-center md:justify-start;
    animation: moveRight 20s linear infinite;
  }
  
  .dot-word {
    @apply transition-all duration-300 ease-in-out;
    animation: fadeIn 0.5s ease-out both;
  }
  
  .dot-word:hover {
    transform: scale(1.05);
  }
  
  .dot-character {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .dot-character:hover {
    transform: scale(1.1);
  }
  
  .dot-row {
    @apply mb-1;
  }
  
  .dot {
    @apply w-2 h-2 md:w-3 md:h-3 lg:w-4 lg:h-4 rounded-full mx-0.5 transition-all duration-300 ease-in-out;
  }
  
  .dot.active {
    @apply bg-gray-400;
  }
  
  .dot.active:hover {
    @apply bg-red-500 transform scale-125;
  }
  
  .dot.inactive {
    @apply bg-transparent;
  }
  
  .profile-image {
    animation: float 3s ease-in-out infinite;
  }
  
  .profile-image img {
    @apply transition-transform duration-300 ease-in-out;
  }
  
  .profile-image:hover img {
    transform: scale(1.05) rotate(2deg);
  }
  
  /* Animations */
  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(50px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
  
  @keyframes fadeInSlide {
    from {
      opacity: 0;
      transform: translateX(-100px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
  
  @keyframes moveRight {
    0% {
      transform: translateX(-10%);
    }
    100% {
      transform: translateX(10%);
    }
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }
  
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }
  
  /* Mobile responsiveness */
  @media (max-width: 768px) {
    .animated-text-container {
      @apply px-2;
    }
    
    .text-line {
      @apply justify-center mb-3;
    }
    
    .dot-word {
      @apply mx-1;
    }
    
    .profile-image {
      @apply mx-4;
    }
  }
  
  @media (max-width: 480px) {
    .dot {
      @apply w-1.5 h-1.5;
    }
    
    .profile-image img {
      @apply w-16 h-16;
    }
  }
  </style>