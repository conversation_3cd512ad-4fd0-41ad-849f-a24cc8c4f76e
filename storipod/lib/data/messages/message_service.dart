// ignore_for_file: empty_catches

import 'dart:async';
import 'package:storipod_app/data/api/api_function.dart';
import 'package:storipod_app/data/messages/group_service.dart';
import 'package:storipod_app/model/response_model/message_model.dart';
import 'package:storipod_app/model/response_model/user_data_model.dart';
import 'package:storipod_app/res/constant/constant.dart';

class MessageService {
  static final MessageService _instance = MessageService._internal();
  factory MessageService() => _instance;
  MessageService._internal();

  final GroupService _groupService = GroupService();

  final Map<String, UserData> _userCache = <String, UserData>{};
  final Set<String> _loadingUsers = <String>{};

  Future<UserData?> getUserData(String? userId) async {
    if (userId == null || userId.isEmpty) return null;

    if (_userCache.containsKey(userId)) {
      return _userCache[userId];
    }

    if (_loadingUsers.contains(userId)) {
      return null;
    }

    _loadingUsers.add(userId);

    try {
      final user = await _groupService.getUserById(userId);
      if (user != null) {
        final userData = _convertToUserData(user);
        _userCache[userId] = userData;
        return userData;
      }
    } catch (e) {
    } finally {
      _loadingUsers.remove(userId);
    }

    return null;
  }

  Future<void> preloadUsersForMessages(List<MessageModel> messages) async {
    final userIds =
        messages.map((msg) => msg.senderId).where((id) => id != null && id.isNotEmpty).cast<String>().toSet();

    await _preloadUserIds(userIds);
  }

  Future<void> preloadUserIds(List<String> userIds) async {
    await _preloadUserIds(userIds.toSet());
  }

  Future<void> _preloadUserIds(Set<String> userIds) async {
    final missingUserIds = userIds.where((id) => !_userCache.containsKey(id)).toList();

    if (missingUserIds.isEmpty) return;

    final futures = missingUserIds.map((userId) => getUserData(userId));
    await Future.wait(futures);
  }

  Future<bool> sendMessage({
    required String conversationId,
    required String content,
    String? replyToMessageId,
  }) async {
    try {
      final response = await APIFunction().postApiCall(
        apiName: Constant.messages,
        params: {
          'roomId': conversationId,
          'content': content,
          if (replyToMessageId != null) 'replyToMessageId': replyToMessageId,
        },
      );

      if (response != null) {
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  Future<bool> deleteMessages({
    required String roomId,
    required List<String> messageIds,
  }) async {
    try {
      final response = await APIFunction().deleteApiCall(
        apiName: '${Constant.activeChat}/$roomId/messages',
        params: {
          'messageIds': messageIds,
        },
      );

      if (response != null) {
        return true;
      }

      return false;
    } catch (e) {
      return false;
    }
  }

  Future<void> clearUserCache() async {
    _userCache.clear();
    _loadingUsers.clear();
  }

  int get cachedUserCount => _userCache.length;

  bool isUserLoading(String userId) => _loadingUsers.contains(userId);

  bool isUserCached(String userId) => _userCache.containsKey(userId);

  UserData? getCachedUser(String? userId) => userId != null ? _userCache[userId] : null;

  UserData _convertToUserData(User user) {
    return UserData(
      id: user.id,
      fname: user.fname,
      lname: user.lname,
      handle: user.handle,
      profilePicture: user.profilePicture,
    );
  }
}
