// ignore_for_file: depend_on_referenced_packages

import 'dart:async';
import 'package:collection/collection.dart';
import 'package:storipod_app/data/api/api_function.dart';
import 'package:storipod_app/model/response_model/message_reaction_model.dart';
import 'package:storipod_app/model/response_model/reaction_model.dart';
import 'package:storipod_app/res/constant/constant.dart';
import 'package:storipod_app/utils/cache/cache_helper.dart';

class ReactionService {
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 1);

  static Future<MessageReactionModel> addReaction(
    String messageId,
    String reactionId,
  ) async {
    int attempts = 0;

    while (attempts < _maxRetries) {
      try {
        final response = await APIFunction().postApiCall(
          apiName: '${Constant.messages}/$messageId/reactions',
          params: {
            'reactionId': reactionId,
          },
        );

        if (response != null) {
          return MessageReactionModel.from<PERSON>son(response);
        } else {
          throw Exception('Failed to add reaction: Empty response');
        }
      } catch (e) {
        attempts++;
        if (attempts >= _maxRetries) {
          throw Exception('Failed to add reaction after $_maxRetries attempts: $e');
        }

        await Future.delayed(_retryDelay * attempts);
      }
    }

    throw Exception('Failed to add reaction after $_maxRetries attempts');
  }

  static Future<bool> removeReaction(String messageId, String reactionId) async {
    int attempts = 0;

    while (attempts < _maxRetries) {
      try {
        final response = await APIFunction().deleteApiCall(
          apiName: '${Constant.messages}/$messageId/reactions',
          params: {
            'reactionId': reactionId,
          },
        );

        if (response != null) {
          return true;
        } else {
          throw Exception('Failed to remove reaction: Empty response');
        }
      } catch (e) {
        attempts++;
        if (attempts >= _maxRetries) {
          throw Exception('Failed to remove reaction after $_maxRetries attempts: $e');
        }

        await Future.delayed(_retryDelay * attempts);
      }
    }

    throw Exception('Failed to remove reaction after $_maxRetries attempts');
  }

  static Future<Map<String, dynamic>> getReactions(String messageId) async {
    try {
      final response = await APIFunction().getApiCall(
        apiName: '${Constant.messages}/$messageId/reactions',
      );

      if (response != null && response['success'] == true) {
        return response;
      } else {
        throw Exception('Failed to get reactions: ${response?['message'] ?? 'Unknown error'}');
      }
    } catch (e) {
      throw Exception('Failed to get reactions: $e');
    }
  }

  static Future<void> initializeReactions() async {
    try {
      final response = await APIFunction().getApiCall(
        apiName: Constant.reactions,
      );

      if (response != null) {
        final reactionModel = reactionModelFromJson(response);
        if (reactionModel.isNotEmpty) {
          CacheHelper.saveReactionsType(reactionModel);
        }
      }
    } catch (e) {
      throw Exception('Failed to initialize reactions: $e');
    }
  }

  static List<ReactionModel> getAvailableReactions() {
    return CacheHelper.getReactionsType() ?? [];
  }

  static String? getReactionIdByEmoji(String emoji) {
    final reactions = getAvailableReactions();
    final reaction = reactions.firstWhereOrNull((r) => r.emoji == emoji);
    return reaction?.id;
  }

  static String? getEmojiByReactionId(String reactionId) {
    final reactions = getAvailableReactions();
    final reaction = reactions.firstWhereOrNull((r) => r.id == reactionId);
    return reaction?.emoji;
  }
}
