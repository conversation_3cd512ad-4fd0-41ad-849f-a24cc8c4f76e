import 'package:storipod_app/data/api/api_function.dart';
import 'package:storipod_app/res/constant/constant.dart';

class AdService {
  static final AdService _instance = AdService._internal();
  factory AdService() => _instance;
  AdService._internal();

  /// Track ad interaction (view, click, impression)
  ///
  /// [adId] - The ID of the ad
  /// [interactionType] - Type of interaction: 'view', 'click', or 'impression'
  /// [duration] - Duration in seconds (required for 'view' type)
  Future<bool> _trackInteraction({
    required String adId,
    required String interactionType,
    int? duration,
    bool isPodAd = false,
  }) async {
    try {
      final Map<String, dynamic> payload = {'adId': adId, 'interactionType': interactionType, 'duration': ?duration};

      final endpoint = isPodAd ? Constant.podAdsInteractions : Constant.adsInteractions;

      final response = await APIFunction().postApiCall(apiName: endpoint, params: payload, showErrorToast: false);

      return response != null;
    } catch (e) {
      return false;
    }
  }

  Future<bool> trackView({required String adId, required int duration, bool isPodAd = false}) async =>
      _trackInteraction(adId: adId, interactionType: 'view', duration: duration, isPodAd: isPodAd);

  Future<bool> trackClick({required String adId, bool isPodAd = false}) async =>
      _trackInteraction(adId: adId, interactionType: 'click', isPodAd: isPodAd);

  Future<bool> trackImpression({required String adId, bool isPodAd = false}) async =>
      _trackInteraction(adId: adId, interactionType: 'impression', isPodAd: isPodAd);
}
