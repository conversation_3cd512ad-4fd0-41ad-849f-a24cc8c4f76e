# Option 1: Use your ACR (recommended for production)
#FROM --platform=linux/amd64 storipodmlacrprod.azurecr.io/python:3.12-slim

# Option 2: Use Microsoft Container Registry (no authentication required)
# FROM mcr.microsoft.com/python:3.12-slim

# Option 3: Use official Python image (requires Docker Hub auth)
FROM python:3.12-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy pyproject.toml first to leverage Docker cache
COPY pyproject.toml ./

# Install dependencies using pip (more reliable in Docker)
RUN pip install --no-cache-dir -e .

# Copy the rest of the application
COPY . .

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Default command (will be overridden by ACI)
CMD ["python", "-m", "src.entrypoint.run_pipelines"] 
