# Storipod Features Service

[![Python 3.12](https://img.shields.io/badge/python-3.12-blue.svg)](https://www.python.org/downloads/)
[![Code style: ruff](https://img.shields.io/badge/code%20style-ruff-000000.svg)](https://github.com/astral-sh/ruff)
[![Tests](https://img.shields.io/badge/tests-pytest-green.svg)](https://pytest.org/)
[![Coverage](https://img.shields.io/badge/coverage-100%25-brightgreen.svg)](https://coverage.readthedocs.io/)

A comprehensive Python service for managing and processing features used in the Storipod recommendation system. This project extracts, transforms, and stores features related to users, stories, interactions, and embeddings for use in content recommendation algorithms.

## Table of Contents

- [Project Overview](#project-overview)
- [Features](#features)
- [Architecture](#architecture)
- [Project Structure](#project-structure)
- [Technical Stack](#technical-stack)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Configuration](#configuration)
- [Usage](#usage)
- [API Reference](#api-reference)
- [Development](#development)
- [Testing](#testing)
- [Deployment](#deployment)
- [Performance](#performance)
- [Troubleshooting](#troubleshooting)
- [Contributing](#contributing)
- [License](#license)

## Project Overview

The Storipod Features Service is a critical component of the Storipod recommendation ecosystem, designed to handle large-scale feature engineering and data processing for content recommendation algorithms. Built with performance and scalability in mind, this service processes millions of user interactions and content features to power personalized recommendations.

### Key Objectives

- **Scalable Feature Engineering**: Process large volumes of user and content data efficiently
- **Real-time Data Pipeline**: Support near real-time feature updates for dynamic recommendations
- **Data Quality Assurance**: Implement comprehensive validation and monitoring
- **Modular Architecture**: Enable easy extension and maintenance of feature types
- **Cloud-Native Design**: Leverage Azure cloud services for scalability and reliability

## Features

### 🚀 **Core Capabilities**

- **User Feature Processing**
  - Account age and engagement metrics
  - Activity patterns and preferences
  - Demographic and behavioral features
  - Historical interaction analysis

- **Story Feature Engineering**
  - Content metadata and metrics
  - Engagement statistics (views, reactions, comments)
  - Content quality scoring
  - Category and topic analysis

- **Interaction Feature Generation**
  - User-story interaction matrices
  - Engagement type classification
  - Temporal interaction patterns
  - Recommendation feedback loops

- **Embedding Management**
  - Semantic similarity vectors
  - Content and user embeddings
  - Vector storage and retrieval
  - Similarity computation

### 🛡️ **Data Quality & Reliability**

- Comprehensive input validation
- Data consistency checks
- Error handling and recovery
- Monitoring and alerting
- Audit trails and logging

### ☁️ **Cloud Integration**

- Azure Blob Storage for feature persistence
- PostgreSQL database connectivity
- Scalable compute resources
- Secure configuration management

## Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Data Sources  │    │  Feature Engine │    │   Storage Layer │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ PostgreSQL  │ │───▶│ │ User Feature│ │───▶│ │ Azure Blob  │ │
│ │ Database    │ │    │ │ Processor   │ │    │ │ Storage     │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ API Events  │ │───▶│ │Story Feature│ │───▶│ │ Feature     │ │
│ │ Stream      │ │    │ │ Processor   │ │    │ │ Database    │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │ User Events │ │───▶│ │Interaction  │ │───▶│ │ Monitoring  │ │
│ │ Log         │ │    │ │ Processor   │ │    │ │ Dashboard   │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Project Structure

```
storipod-features-service/
├── config.yml                     # Configuration settings
├── Makefile                       # Build automation
├── pyproject.toml                 # Project metadata and dependencies
├── pytest.ini                     # Test configuration
├── requirements.txt               # Production dependencies
├── requirements-dev.txt           # Development dependencies
├── src/
│   ├── embeddings_pipeline.py     # Pipeline for embedding generation
│   ├── feature_pipeline.py        # Main feature processing pipeline
│   ├── features/
│   │   ├── embedding_feature.py   # Embedding feature processing
│   │   ├── interaction_feature.py # User-story interaction features
│   │   ├── story_feature.py       # Story content features
│   │   └── user_feature.py        # User profile features
│   ├── repositories/
│   │   ├── base_repository.py     # Base data access patterns
│   │   ├── category_repository.py # Category data access
│   │   ├── embedding_repository.py # Embedding data access
│   │   ├── inference_data_service.py # Inference data handling
│   │   ├── story_repository.py    # Story data access
│   │   └── user_repository.py     # User data access
│   ├── services/
│   │   ├── candidate_retrieval_service.py # Recommendation candidate service
│   │   └── validation.py          # Data validation service
│   └── utils/
│       ├── azure_blob_file_util.py # Azure storage utilities
│       ├── config.py              # Configuration management
│       ├── constants.py           # System constants
│       └── sql_queries.py         # SQL query definitions
└── tests/
    ├── conftest.py                # Test fixtures
    └── unit/                      # Unit tests
        ├── features/              # Feature processing tests
        ├── repositories/          # Repository tests
        └── services/              # Service tests
```

## Technical Stack

### Core Technologies
- **Python**: 3.12+ (Type hints, async support)
- **Data Processing**: pandas, numpy, pyarrow
- **Database**: SQLAlchemy (ORM), psycopg2 (PostgreSQL driver)
- **Configuration**: Pydantic (validation), python-dotenv, PyYAML
- **Cloud Storage**: Azure Storage Blob SDK

### Development & Quality
- **Testing**: pytest, pytest-cov, pytest-mock
- **Code Quality**: ruff (linting & formatting)
- **Logging**: Loguru (structured logging)
- **Type Checking**: Built-in type hints
- **Documentation**: Sphinx-compatible docstrings

### Infrastructure
- **Container**: Docker support
- **Orchestration**: Kubernetes ready
- **Monitoring**: Integrated logging and metrics
- **CI/CD**: GitHub Actions compatible

## Prerequisites

Before installing the Storipod Features Service, ensure you have:

### System Requirements
- **Python**: 3.12 or higher
- **Memory**: Minimum 4GB RAM (8GB+ recommended for large datasets)
- **Storage**: 10GB+ free disk space
- **Network**: Stable internet connection for cloud services

### External Dependencies
- **PostgreSQL**: 12+ (source database)
- **Azure Storage Account**: For feature persistence
- **Git**: For version control and deployment

### Development Tools (Optional)
- **Docker**: For containerized development
- **VS Code**: Recommended IDE with Python extensions
- **Postman**: For API testing

## Installation

### Quick Start

1. **Clone the repository:**

   ```bash
   git clone https://github.com/storipod/storipod-features-service.git
   cd storipod-features-service
   ```

2. **Set up Python environment:**

   ```bash
   # Create virtual environment
   python -m venv .venv
   
   # Activate virtual environment
   # On macOS/Linux:
   source .venv/bin/activate
   
   # On Windows:
   .venv\Scripts\activate
   ```

3. **Install dependencies:**

   ```bash
   # Production dependencies
   pip install -r requirements.txt
   
   # Development dependencies (optional)
   pip install -r requirements-dev.txt
   
   # Or install in development mode
   pip install -e .
   ```

4. **Verify installation:**

   ```bash
   # Check Python version
   python --version  # Should be 3.12+
   
   # Run basic health check
   python -c "import src; print('Installation successful!')"
   ```

### Docker Installation (Alternative)

```bash
# Build Docker image
docker build -t storipod-features-service .

# Run container
docker run -d --name features-service \
  --env-file .env \
  -v $(pwd)/config.yml:/app/config.yml \
  storipod-features-service
```

## Configuration

### Environment Variables

Create a `.env` file in the root directory:

```bash
# Environment
ENV=development  # Options: development, staging, production

# Source Database Configuration
DB_SOURCE_USERNAME=your_db_username
DB_SOURCE_PASSWORD=your_db_password
DB_SOURCE_HOST=localhost
DB_SOURCE_PORT=5432
DB_SOURCE_DATABASE=storipod_source

# Recommendation Database Configuration  
DB_RECSYS_USERNAME=your_recsys_username
DB_RECSYS_PASSWORD=your_recsys_password
DB_RECSYS_HOST=localhost
DB_RECSYS_PORT=5432
DB_RECSYS_DATABASE=storipod_recsys

# Azure Storage Configuration
AZURE_STORAGE_CONNECTION_STRING=DefaultEndpointsProtocol=https;AccountName=...
AZURE_CONTAINER_NAME=features-storage

# Logging Configuration
LOG_LEVEL=INFO  # Options: DEBUG, INFO, WARNING, ERROR
LOG_FORMAT=json  # Options: json, text
```

### Pipeline Configuration

Configure `config.yml` for pipeline settings:

```yaml
pipeline:
  # Feature table names
  user_feature_table: "user_features"
  story_feature_table: "story_features"
  interaction_feature_table: "interaction_features"
  embedding_feature_table: "embedding_features"
  
  # Processing parameters
  story_window_length: 30  # Days to look back for story features
  max_account_age: 365     # Maximum account age in days
  batch_size: 10000        # Processing batch size
  
  # Quality thresholds
  min_interaction_count: 5
  min_story_views: 10
  data_quality_threshold: 0.95

# Azure Storage Configuration
storage:
  container_name: "storipod-features"
  blob_prefix: "features/v1/"
  compression: "gzip"
  
# Monitoring and Alerts
monitoring:
  enable_metrics: true
  alert_thresholds:
    error_rate: 0.05
    processing_time: 3600  # seconds
```

### Database Schema Setup

```sql
-- Create required tables (run in target database)
CREATE SCHEMA IF NOT EXISTS features;

-- User features table
CREATE TABLE features.user_features (
    user_id VARCHAR(50) PRIMARY KEY,
    account_age_days INTEGER,
    followers_count INTEGER,
    followings_count INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Story features table  
CREATE TABLE features.story_features (
    story_id VARCHAR(50) PRIMARY KEY,
    view_count INTEGER DEFAULT 0,
    reaction_count INTEGER DEFAULT 0,
    comment_count INTEGER DEFAULT 0,
    save_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Interaction features table
CREATE TABLE features.interaction_features (
    user_id VARCHAR(50),
    story_id VARCHAR(50),
    interacted INTEGER DEFAULT 0,
    event_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (user_id, story_id)
);
```

## Usage

### Command Line Interface

#### Feature Pipeline

Run the main feature processing pipeline:

```bash
# Full pipeline execution
python -m src.feature_pipeline

# Process specific feature types
python -m src.feature_pipeline --features user,story
python -m src.feature_pipeline --features interaction

# Process with custom date range
python -m src.feature_pipeline --start-date 2024-01-01 --end-date 2024-01-31

# Dry run (validation only)
python -m src.feature_pipeline --dry-run
```

#### Embeddings Pipeline

Process and generate content embeddings:

```bash
# Generate all embeddings
python -m src.embeddings_pipeline

# Generate embeddings for new content only
python -m src.embeddings_pipeline --incremental

# Batch processing with custom size
python -m src.embeddings_pipeline --batch-size 5000
```

### Programmatic Usage

```python
from src.features.interaction_feature import InteractionFeature
from src.features.user_feature import UserFeature
from src.features.story_feature import StoryFeature

# Initialize feature processors
config = {"story_window_length": 30}
interaction_processor = InteractionFeature(config)
user_processor = UserFeature(config)
story_processor = StoryFeature(config)

# Process features
interaction_features = interaction_processor.create_features(
    saved_stories_df,
    story_slides_df,
    slide_views_df,
    slide_reactions_df,
    slide_comments_df
)

user_features = user_processor.create_features(users_df)
story_features = story_processor.create_features(stories_df, interactions_df)
```

### Monitoring and Health Checks

```bash
# Check service health
python -c "from src.utils.config import app_config; print('Config loaded successfully')"

# Validate configuration
python -m src.utils.config --validate

# Test database connectivity
python -m src.repositories.base_repository --test-connection

# Check Azure storage connectivity
python -m src.utils.azure_blob_file_util --test-connection
```

## API Reference

### Core Classes

#### `InteractionFeature`

Processes user-story interaction data to create binary interaction features.

```python
class InteractionFeature:
    def __init__(self, config: Optional[Dict] = None)
    
    def create_features(
        self,
        saved_stories: pd.DataFrame,
        story_slides: pd.DataFrame, 
        slide_views: pd.DataFrame,
        slide_reactions: pd.DataFrame,
        slide_comments: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Creates binary interaction features for user-story pairs.
        
        Returns:
            DataFrame with columns: user_id, story_id, interacted, event_timestamp
        """
```

#### `UserFeature`

Processes user profile data and engagement metrics.

```python
class UserFeature:
    def create_features(self, users_df: pd.DataFrame) -> pd.DataFrame:
        """
        Creates user-level features including account age and engagement metrics.
        
        Returns:
            DataFrame with user features
        """
```

#### `StoryFeature`

Processes story content and engagement features.

```python
class StoryFeature:  
    def create_features(
        self, 
        stories_df: pd.DataFrame,
        interactions_df: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Creates story-level features including engagement and quality metrics.
        
        Returns:
            DataFrame with story features
        """
```

### Configuration Classes

#### `AppConfig`

Main configuration class with pipeline settings.

```python
@dataclass
class AppConfig:
    pipeline: PipelineConfig
    storage: StorageConfig
    monitoring: MonitoringConfig
```

### Utility Functions

#### Data Validation

```python
from src.services.validation import validate_dataframe, check_data_quality

# Validate DataFrame structure
validate_dataframe(df, required_columns=['user_id', 'story_id'])

# Check data quality metrics
quality_score = check_data_quality(df, thresholds={'null_rate': 0.05})
```

#### Azure Storage

```python
from src.utils.azure_blob_file_util import AzureBlobFileUtil

# Upload features to Azure
storage = AzureBlobFileUtil()
storage.upload_dataframe(features_df, 'features/user_features_2024.parquet')

# Download features from Azure  
features_df = storage.download_dataframe('features/user_features_2024.parquet')
```

## Development

### Setting Up Development Environment

1. **Fork and clone the repository:**

   ```bash
   git clone https://github.com/yourusername/storipod-features-service.git
   cd storipod-features-service
   ```

2. **Create development environment:**

   ```bash
   python -m venv .venv
   source .venv/bin/activate
   pip install -r requirements-dev.txt
   pip install -e .
   ```

3. **Set up pre-commit hooks:**

   ```bash
   pre-commit install
   ```

4. **Copy environment template:**

   ```bash
   cp .env.example .env
   # Edit .env with your development settings
   ```

### Development Workflow

```bash
# Create feature branch
git checkout -b feature/your-feature-name

# Make changes and commit
git add .
git commit -m "feat: add new feature"

# Push and create PR
git push origin feature/your-feature-name
```

## Testing

### Running Tests

```bash
# Run all tests
pytest

# Run specific test file
pytest tests/unit/features/test_interaction_feature.py

# Run tests with coverage
pytest --cov=src --cov-report=html

# Run tests with verbose output
pytest -v

# Run tests for specific module
pytest tests/unit/features/ -k "test_create_features"
```

### Test Categories

```bash
# Unit tests only
pytest tests/unit/

# Integration tests only  
pytest tests/integration/

# Performance tests
pytest tests/performance/ --benchmark-only

# Run tests with markers
pytest -m "slow"  # Run slow tests
pytest -m "not slow"  # Skip slow tests
```

### Writing Tests

Follow these patterns when writing tests:

```python
import pytest
import pandas as pd
from src.features.interaction_feature import InteractionFeature

class TestInteractionFeature:
    @pytest.fixture
    def sample_data(self):
        return pd.DataFrame({
            'user_id': ['user1', 'user2'],
            'story_id': ['story1', 'story2']
        })
    
    def test_feature_creation(self, sample_data):
        processor = InteractionFeature()
        result = processor.create_features(sample_data)
        assert len(result) > 0
        assert 'interacted' in result.columns
```

### Code Quality

#### Linting and Formatting

```bash
# Check code style
ruff check .

# Fix auto-fixable issues
ruff check . --fix

# Format code
ruff format .

# Check types (if using mypy)
mypy src/
```

#### Code Coverage

```bash
# Generate coverage report
pytest --cov=src --cov-report=html

# View coverage in browser
open htmlcov/index.html

# Coverage with missing lines
pytest --cov=src --cov-report=term-missing
```

### Performance Testing

```bash
# Run performance benchmarks
pytest tests/performance/ --benchmark-only

# Profile specific functions
python -m cProfile -o profile.stats -m src.feature_pipeline
python -c "import pstats; pstats.Stats('profile.stats').sort_stats('cumulative').print_stats(20)"

# Memory profiling
pip install memory-profiler
python -m memory_profiler src/feature_pipeline.py
```

## Deployment

### Environment Setup

#### Staging Environment

```bash
# Set environment variables
export ENV=staging
export DB_SOURCE_HOST=staging-db.example.com
export AZURE_STORAGE_CONNECTION_STRING=...

# Deploy to staging
./scripts/deploy-staging.sh
```

#### Production Environment

```bash
# Set environment variables
export ENV=production
export DB_SOURCE_HOST=prod-db.example.com
export AZURE_STORAGE_CONNECTION_STRING=...

# Deploy to production
./scripts/deploy-production.sh
```

### Docker Deployment

```bash
# Build production image
docker build -t storipod-features-service:latest .

# Run with docker-compose
docker-compose up -d

# Scale service
docker-compose up --scale features-service=3
```

### Kubernetes Deployment

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: features-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: features-service
  template:
    metadata:
      labels:
        app: features-service
    spec:
      containers:
      - name: features-service
        image: storipod-features-service:latest
        env:
        - name: ENV
          value: "production"
        - name: DB_SOURCE_HOST
          valueFrom:
            secretKeyRef:
              name: database-secrets
              key: host
```

```bash
# Deploy to Kubernetes
kubectl apply -f k8s/
kubectl rollout status deployment/features-service
```

### Monitoring Setup

```bash
# Health check endpoint
curl http://localhost:8080/health

# Metrics endpoint
curl http://localhost:8080/metrics

# Check logs
kubectl logs -f deployment/features-service
```

## Performance

### Optimization Guidelines

#### Data Processing
- Use vectorized pandas operations
- Implement batch processing for large datasets
- Use appropriate data types (int32 vs int64)
- Leverage pandas categorical data for string columns
- Use parquet format for efficient storage

#### Memory Management
```python
# Process data in chunks
def process_large_dataset(df, chunk_size=10000):
    for chunk in pd.read_csv('large_file.csv', chunksize=chunk_size):
        processed_chunk = process_chunk(chunk)
        yield processed_chunk

# Use context managers for resources
with pd.HDFStore('data.h5') as store:
    store['features'] = features_df
```

#### Database Optimization
```python
# Use connection pooling
engine = create_engine(
    connection_string,
    pool_size=10,
    max_overflow=20,
    pool_pre_ping=True
)

# Batch database operations
df.to_sql('features', engine, if_exists='append', method='multi')
```

### Performance Benchmarks

| Operation | Dataset Size | Processing Time | Memory Usage |
|-----------|-------------|-----------------|--------------|
| User Features | 1M users | 2.3 seconds | 500MB |
| Story Features | 100K stories | 5.1 seconds | 800MB |
| Interactions | 10M interactions | 12.4 seconds | 1.2GB |
| Embeddings | 50K vectors | 8.7 seconds | 2.1GB |

### Scaling Considerations

- **Horizontal Scaling**: Deploy multiple instances with load balancing
- **Database Scaling**: Use read replicas for data extraction
- **Storage Scaling**: Implement data partitioning by date/region
- **Caching**: Use Redis for frequently accessed features

## Troubleshooting

### Common Issues

#### Database Connection Issues

```bash
# Test database connectivity
python -c "
from src.repositories.base_repository import BaseRepository
repo = BaseRepository()
print('Database connection successful!')
"

# Check connection parameters
echo $DB_SOURCE_HOST
echo $DB_SOURCE_PORT
```

**Solution**: Verify environment variables and network connectivity.

#### Memory Issues

```bash
# Monitor memory usage
top -p $(pgrep -f "python.*feature_pipeline")

# Check available memory
free -h
```

**Solution**: Reduce batch size or increase available memory.

#### Azure Storage Issues

```bash
# Test Azure connectivity
python -c "
from src.utils.azure_blob_file_util import AzureBlobFileUtil
util = AzureBlobFileUtil()
print('Azure storage connection successful!')
"
```

**Solution**: Verify connection string and container permissions.

### Debugging Tips

#### Enable Debug Logging

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Or set environment variable
export LOG_LEVEL=DEBUG
```

#### Use Interactive Debugging

```python
# Add breakpoint in code
import pdb; pdb.set_trace()

# Or use ipdb for better interface
import ipdb; ipdb.set_trace()
```

#### Profile Performance Issues

```bash
# Profile specific function
python -m cProfile -s cumulative -m src.feature_pipeline

# Use line profiler
pip install line_profiler
kernprof -l -v src/features/interaction_feature.py
```

### Getting Help

1. **Check logs**: Review application logs for error details
2. **Search issues**: Check existing GitHub issues
3. **Create issue**: Provide error logs and reproduction steps
4. **Contact team**: Reach out to the development team

## Contributing

### Development Process

1. **Issue Creation**: Create or claim an issue
2. **Branch Creation**: Create feature branch from main
3. **Development**: Implement changes with tests
4. **Code Review**: Submit PR for review
5. **Testing**: Ensure all tests pass
6. **Deployment**: Merge to main after approval

### Code Standards

#### Style Guidelines
- Follow PEP 8 Python style guide
- Use type hints for all functions
- Write comprehensive docstrings
- Maintain test coverage above 90%
- Use meaningful variable and function names

#### Commit Messages
```bash
# Format: type(scope): description
feat(features): add interaction feature processor
fix(database): resolve connection timeout issue
docs(readme): update installation instructions
test(features): add comprehensive test coverage
```

#### Pull Request Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature  
- [ ] Breaking change
- [ ] Documentation update

## Testing
- [ ] Unit tests added/updated
- [ ] Integration tests pass
- [ ] Performance tests pass

## Checklist
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated
```

### Review Process

1. **Automated Checks**: All CI/CD checks must pass
2. **Code Review**: At least one approval required
3. **Testing**: Manual testing in staging environment
4. **Documentation**: Update relevant documentation
5. **Deployment**: Merge and deploy to production

## License

Proprietary - Storipod © 2025


<!-- Security scan triggered at 2025-09-02 05:38:06 -->

<!-- Security scan triggered at 2025-09-02 16:16:02 -->