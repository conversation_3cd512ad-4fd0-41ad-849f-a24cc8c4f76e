pipeline:
  user_features_table: "fts_users"
  story_features_table: "fts_stories"
  interaction_features_table: "fts_user_story_interactions"
  story_window_length: 5
  max_account_age: 200
  top_stories_like_weight: 0.3
  top_stories_comment_weight: 0.5
  top_stories_save_weight: 0.2
  chromadb_collection_batch_size: 500
  enable_summarization: false


system_settings:
  ENV: prod
  DB_SOURCE_HOST: prod-storipod-db.postgres.database.azure.com
  DB_SOURCE_PORT: 5432
  DB_SOURCE_DATABASE: storipod
  DB_RECSYS_HOST: prod-storipod-db.postgres.database.azure.com
  DB_RECSYS_PORT: 5432
  DB_RECSYS_DATABASE: storipod-recommendation
  CHROMADB_HOST: chromadb-dev-**********.northeurope.azurecontainer.io
  CHROMADB_PORT: 8000
  CHROMADB_ALLOW_RESET: True
  AZURE_OPENAI_ENDPOINT: https://storipod-openai.openai.azure.com/
  OPENAI_MODEL: gpt-4o-mini
  OPENAI_API_VERSION: 2024-12-01-preview