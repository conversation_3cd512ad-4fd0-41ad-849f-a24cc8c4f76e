
from src.features.story_feature import StoryFeature
from src.repositories.story_repository import StoryRepository
from src.utils.config import env_settings, app_config
from loguru import logger


class TopStoriesPipeline:
    def __init__(self, env_settings, app_config):
        """
        Initialize TopStoriesPipeline with injected dependencies.
        """
        self.env_settings = env_settings
        self.config = app_config

    def run(self):
        """
        Main method to run the Top Stories Pipeline.
        """
        try:
            # Retrieve data
            stories_agg_df, saved_stories_df = self._retrieve_data()
            
            # Create features
            features_df = self._create_features(stories_agg_df, saved_stories_df)
            if features_df.empty:
                logger.error("Features DataFrame is empty after creation.")
                return
            
            # Compute ratings
            features_df = self._compute_ratings(features_df)
            
            # Save features
            self._save_features(features_df)
            
            # Log results
            logger.info(f"TopStories size: {features_df.shape}")
            logger.info("TopStories Feature pipeline completed.")
        except Exception as e:
            logger.exception("Error occurred in TopStoriesPipeline run method: %s", str(e))

    def _retrieve_data(self):
        """
        Retrieve aggregated and saved stories using StoryRepository.
        """
        story_repository = StoryRepository(self.env_settings, self.config)
        stories_agg_df = story_repository.get_stories_aggregate()
        saved_stories_df = story_repository.get_saved_stories()
        return stories_agg_df, saved_stories_df

    def _create_features(self, stories_agg_df, saved_stories_df):
        """
        Create top stories features using StoryFeature.
        """
        story_feature = StoryFeature(self.config)
        return story_feature.create_top_stories_features(stories_agg_df, saved_stories_df)

    def _compute_ratings(self, features_df):
        """
        Compute average and weighted ratings on the features DataFrame.
        """
        story_feature = StoryFeature(self.config)
        features_df = story_feature.compute_average_rating(features_df)
        features_df = story_feature.compute_weighted_rating(features_df)
        return features_df

    def _save_features(self, features_df):
        """
        Save the computed top stories features using StoryRepository.
        """
        story_repository = StoryRepository(self.env_settings, self.config)
        story_repository.save_top_stories(features_df)


if __name__ == "__main__":
    logger.info("TopStories pipeline started...")
    top_stories_pipeline = TopStoriesPipeline(env_settings, app_config)
    top_stories_pipeline.run()
