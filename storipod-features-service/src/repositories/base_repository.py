import pandas as pd
from loguru import logger
from sqlalchemy import create_engine


class BaseRepository:
    def __init__(self, env_settings, config) -> None:
        self.env_settings = env_settings
        self.config = config

    def fetch_data(self, qry_string: str) -> pd.DataFrame:
        try:
            # Create the SQLAlchemy engine
            engine = self.create_sql_engine(
                self.env_settings.DB_SOURCE_USERNAME,
                self.env_settings.DB_SOURCE_PASSWORD.get_secret_value(),
                self.config.system_settings.DB_SOURCE_HOST,
                self.config.system_settings.DB_SOURCE_PORT,
                self.config.system_settings.DB_SOURCE_DATABASE,
            )

            return pd.read_sql_query(qry_string, engine)

        except Exception as e:
            logger.error(f"Error running query: {e}")
            return None

    def create_sql_engine(
        self,
        db_username: str,
        db_password: str,
        db_host: str,
        db_port: str,
        db_name: str,
    ) -> create_engine:
        connection_string = (
            f"postgresql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}"
        )
        return create_engine(connection_string)

    def fetch_feature_data(self, qry_string: str) -> pd.DataFrame:
        try:
            # Create the SQLAlchemy engine
            engine = self.create_sql_engine(
                self.env_settings.DB_RECSYS_USERNAME,
                self.env_settings.DB_RECSYS_PASSWORD.get_secret_value(),
                self.config.system_settings.DB_RECSYS_HOST,
                self.config.system_settings.DB_RECSYS_PORT,
                self.config.system_settings.DB_RECSYS_DATABASE,
            )

            return pd.read_sql_query(qry_string, engine)

        except Exception as e:
            logger.error(f"Error running query: {e}")
            return None
