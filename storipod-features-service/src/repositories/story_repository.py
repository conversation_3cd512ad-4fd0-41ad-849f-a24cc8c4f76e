import sys
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import pandas as pd
import pytz
from loguru import logger
from sqlalchemy import UUID, DateTime, Float, String
from sqlalchemy import text

from src.repositories.base_repository import BaseRepository
from src.utils.sql_queries import SqlQueries


class StoryRepositoryABC(ABC):
    """Abstract base class defining the interface for story repository operations."""

    @abstractmethod
    def get_stories(self) -> pd.DataFrame:
        """Retrieve all stories."""

    @abstractmethod
    def get_saved_stories(self) -> pd.DataFrame:
        """Retrieve all saved stories."""

    @abstractmethod
    def get_story_slides(self) -> pd.DataFrame:
        """Retrieve all story slides."""

    @abstractmethod
    def get_story_slide_views(self) -> pd.DataFrame:
        """Retrieve view statistics for story slides."""

    @abstractmethod
    def get_story_slide_reactions(self) -> pd.DataFrame:
        """Retrieve reaction data for story slides."""

    @abstractmethod
    def get_story_slide_comments(self) -> pd.DataFrame:
        """Retrieve comments for story slides."""

    @abstractmethod
    def get_read_story_ids(self, user_id: str) -> pd.DataFrame:
        """Retrieve IDs of stories read by a specific user."""

    @abstractmethod
    def create_story_features(
        self,
        df: pd.DataFrame,
        table_name: str,
        index_columns: list[str] | None = None,
    ) -> None:
        """Create features for stories and store them in the database."""

    @abstractmethod
    def save_top_stories(self, qualified_stories: pd.DataFrame) -> None:
        """Save top stories to the database."""

    @abstractmethod
    def get_story_features(self, created_at: str) -> pd.DataFrame:
        """Retrieve story features filtered by creation date."""

    @abstractmethod
    def get_all_story_features(self) -> pd.DataFrame:
        """Retrieve all story features without date filtering."""


# Exception message constants
NO_DATA_EXCEPTION_MSG = "No data from source"


class StoryRepository(BaseRepository, StoryRepositoryABC):
    """Implementation of story repository operations."""

    def get_stories(self) -> pd.DataFrame:
        """
        Retrieve all stories after a certain date.

        Returns:
            pd.DataFrame: DataFrame containing stories data

        Raises:
            Exception: If no data is found
        """
        stories_df = self.fetch_data(SqlQueries.GET_STORIES.format("2024-01-01"))
        if stories_df is None or stories_df.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return stories_df

    def get_stories_by_creation_timestamp(
        self, last_creation_timestamp: str
    ) -> pd.DataFrame:
        """
        Retrieve stories created after a specific timestamp.

        Args:
            last_creation_timestamp (str): The timestamp to filter stories by

        Returns:
            pd.DataFrame: DataFrame containing stories created after the timestamp
        """
        stories_df = self.fetch_data(
            SqlQueries.GET_STORIES_BY_LAST_CREATION_TIMESTAMP.format(
                last_creation_timestamp
            )
        )
        if stories_df is None or stories_df.empty:
            logger.info(f"No stories found after {last_creation_timestamp}")
            return pd.DataFrame()
        return stories_df

    def get_stories_aggregate(self) -> pd.DataFrame:
        """
        Retrieve aggregated story data and filter by configured time window.

        Returns:
            pd.DataFrame: DataFrame containing filtered story data

        Raises:
            Exception: If no data is found
        """
        # Calculate the date x days ago from now and make it timezone-aware
        days_ago = datetime.now(pytz.utc) - timedelta(
            days=self.config.pipeline.story_window_length
        )

        stories_df = self.fetch_data(SqlQueries.GET_STORIES_AGGREGATE.format(days_ago))
        if stories_df is None or stories_df.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)

        return stories_df        

    def get_saved_stories_by_created_at(self) -> pd.DataFrame:
        """
        Retrieve all saved stories.

        Returns:
            pd.DataFrame: DataFrame containing saved stories data

        Raises:
            Exception: If no data is found
        """
        days_ago = datetime.now(pytz.utc) - timedelta(
            days=self.config.pipeline.story_window_length
        )
        
        saved_stories = self.fetch_data(SqlQueries.GET_SAVED_STORIES_BY_CREATED_AT.format(days_ago))
        if saved_stories is None or saved_stories.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return saved_stories

    def get_saved_stories(self) -> pd.DataFrame:
        """
        Retrieve all saved stories.

        Returns:
            pd.DataFrame: DataFrame containing saved stories data

        Raises:
            Exception: If no data is found
        """
        saved_stories = self.fetch_data(SqlQueries.GET_SAVED_STORIES)
        if saved_stories is None or saved_stories.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return saved_stories

    def get_story_slides_aggregate(self) -> pd.DataFrame:
        """
        Retrieve aggregated story slides data.

        Returns:
            pd.DataFrame: DataFrame containing aggregated story slides

        Raises:
            Exception: If no data is found
        """
        story_slides = self.fetch_data(SqlQueries.GET_STORY_SLIDES_AGGREGATE)
        if story_slides is None or story_slides.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return story_slides

    def get_story_slides(self) -> pd.DataFrame:
        """
        Retrieve all story slides.

        Returns:
            pd.DataFrame: DataFrame containing story slides

        Raises:
            Exception: If no data is found
        """
        story_slides = self.fetch_data(SqlQueries.GET_STORY_SLIDES)
        if story_slides is None or story_slides.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return story_slides

    def get_story_slides_by_story_created_at(
        self, story_created_at: str
    ) -> pd.DataFrame:
        """
        Retrieve story slides for stories created at a specific time.

        Args:
            story_created_at (str): The creation timestamp to filter by

        Returns:
            pd.DataFrame: DataFrame containing filtered story slides

        Raises:
            Exception: If no data is found
        """
        story_slides = self.fetch_data(
            SqlQueries.GET_STORY_SLIDES_BY_STORY_CREATED_AT.format(story_created_at)
        )
        if story_slides is None or story_slides.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return story_slides

    def get_story_slide_views(self) -> pd.DataFrame:
        """
        Retrieve view statistics for story slides.

        Returns:
            pd.DataFrame: DataFrame containing view data

        Raises:
            Exception: If no data is found
        """
        slide_views = self.fetch_data(SqlQueries.GET_SLIDE_VIEWS)
        if slide_views is None or slide_views.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return slide_views

    def get_story_slide_reactions(self) -> pd.DataFrame:
        """
        Retrieve reaction data for story slides.

        Returns:
            pd.DataFrame: DataFrame containing reaction data

        Raises:
            Exception: If no data is found
        """
        slide_reactions = self.fetch_data(SqlQueries.GET_SLIDE_REACTIONS)
        if slide_reactions is None or slide_reactions.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return slide_reactions

    def get_story_slide_comments(self) -> pd.DataFrame:
        """
        Retrieve comments for story slides.

        Returns:
            pd.DataFrame: DataFrame containing comments data

        Returns empty DataFrame if no data is found
        """
        comments_data = self.fetch_data(SqlQueries.GET_SLIDE_COMMENTS)
        if comments_data is None or comments_data.empty:
            logger.info("No comments data found")
            return pd.DataFrame()
        return comments_data

    def get_read_story_ids(self, user_id: str) -> pd.DataFrame:
        """
        Retrieve IDs of stories read by a specific user.

        Args:
            user_id (str): The ID of the user

        Returns:
            pd.DataFrame: DataFrame containing story IDs

        Returns empty DataFrame if no data is found
        """
        story_ids = self.fetch_data(SqlQueries.GET_READ_STORY_IDS.format(user_id))
        if story_ids is None or story_ids.empty:
            logger.info(f"No read stories found for user {user_id}")
            return pd.DataFrame()
        return story_ids

    def get_read_story_ids_batch(self, user_ids: list[str]) -> dict[str, pd.DataFrame]:
        """
        Retrieve IDs of stories read by multiple users.

        Args:
            user_ids (List[str]): List of user IDs

        Returns:
            Dict[str, pd.DataFrame]: Dictionary mapping user IDs to DataFrames
                of read story IDs
        """
        read_story_ids_batch = {}
        read_story_ids_data = self.fetch_data(
            SqlQueries.GET_READ_STORY_IDS.format("','".join(map(str, user_ids)))
        )

        # Return empty dict if no data found
        if read_story_ids_data is None or read_story_ids_data.empty:
            logger.info(f"No read stories found for any of the {len(user_ids)} users")
            return read_story_ids_batch

        # Create dictionary mapping each user to their read stories
        for user_id in user_ids:
            user_read_stories = read_story_ids_data[
                read_story_ids_data["user_id"] == user_id
            ]
            read_story_ids_batch[user_id] = user_read_stories
            logger.debug(
                f"Found {len(user_read_stories)} read stories for user {user_id}"
            )

        return read_story_ids_batch

    def save_top_stories(self, qualified_stories: pd.DataFrame) -> None:
        """
        Save top stories to the database.

        Args:
            qualified_stories (pd.DataFrame): DataFrame containing stories to save
        """
        engine = None
        try:
            # Create database engine
            engine = self.create_sql_engine(
                self.env_settings.DB_SOURCE_USERNAME,
                self.env_settings.DB_SOURCE_PASSWORD.get_secret_value(),
                self.config.system_settings.DB_SOURCE_HOST,
                self.config.system_settings.DB_SOURCE_PORT,
                self.config.system_settings.DB_SOURCE_DATABASE,
            )

            # Group by user_id and select top 1 story with highest rating for each user
            logger.info(f"Grouping {len(qualified_stories)} stories by user_id and selecting top 1 per user")
            
            # Group by user_id and get the story with highest rating for each user
            top_stories_per_user = qualified_stories.loc[
                qualified_stories.groupby('user_id')['rating'].idxmax()
            ].copy()
            
            top_stories_per_user.sort_values(by="rating", ascending=False, inplace=True)
            logger.info(f"Selected {len(top_stories_per_user)} top stories (1 per user) out of {len(qualified_stories)} total stories")
            
            # Use the filtered stories for further processing
            qualified_stories = top_stories_per_user

            # Extract and prepare data
            qualified_stories = qualified_stories[["id", "rating", "score"]]
            qualified_stories = qualified_stories.rename(columns={"id": "story_id"})
            qualified_stories.loc[:, "created_at"] = datetime.now()

            # Limit the number of rows if needed
            max_rows = getattr(self.config, "max_top_stories", 100)
            if len(qualified_stories) > max_rows:
                logger.info(f"Limiting top stories to {max_rows} rows")
                qualified_stories = qualified_stories[:max_rows]

            # Save to database
            qualified_stories.to_sql(
                "top_stories_recommendation",
                con=engine,
                if_exists="replace",
                index=False,
                dtype={
                    "story_id": UUID,
                    "score": Float,
                    "rating": Float,
                    "created_at": DateTime,
                },
            )
            logger.info(f"Saved {len(qualified_stories)} top stories to database")
        finally:
            if engine:
                engine.dispose()

    def create_story_features(
        self,
        df: pd.DataFrame,
        table_name: str,
        index_columns: list[str] | None = None,
    ) -> None:
        """
        Create features for stories and store them in the database.

        Args:
            df (pd.DataFrame): DataFrame containing story features
            table_name (str): Name of the table to store the features in
            index_columns (Optional[List[str]]): List of columns to use as indices
        """
        engine = None
        try:
            engine = self.create_sql_engine(
                self.env_settings.DB_RECSYS_USERNAME,
                self.env_settings.DB_RECSYS_PASSWORD.get_secret_value(),
                self.config.system_settings.DB_RECSYS_HOST,
                self.config.system_settings.DB_RECSYS_PORT,
                self.config.system_settings.DB_RECSYS_DATABASE,
            )

            # Set index if specified
            index = False
            if index_columns:
                df.set_index(index_columns, inplace=True)
                index = True

            # Truncate the table first
            with engine.connect() as connection:
                connection.execute(text(f'TRUNCATE TABLE {table_name}'))
                connection.commit()

            df.to_sql(table_name, engine, if_exists="append", index=index)
            logger.info(
                f"Created story features in table '{table_name}' with {len(df)} rows"
            )
        finally:
            if engine:
                engine.dispose()

    def get_story_features(self, created_at: str) -> pd.DataFrame:
        """
        Retrieve story features from the database filtered by creation date.
        """
        story_features = self.fetch_feature_data(
            SqlQueries.GET_STORY_FEATURES.format(created_at)
        )
        if story_features is None or story_features.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return story_features

    def get_all_story_features(self) -> pd.DataFrame:
        """
        Retrieve all story features from the database without date filtering.
        """
        story_features = self.fetch_feature_data(SqlQueries.GET_FTS_STORIES)
        if story_features is None or story_features.empty:
            raise Exception(NO_DATA_EXCEPTION_MSG)
        return story_features
