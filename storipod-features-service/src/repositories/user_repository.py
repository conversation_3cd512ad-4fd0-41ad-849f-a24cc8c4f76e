from abc import ABC, abstractmethod
from typing import List, Optional

import pandas as pd
from loguru import logger
from sqlalchemy.exc import SQLAlchemyError

from src.repositories.base_repository import BaseRepository
from src.utils.sql_queries import SqlQueries
from sqlalchemy import text

class UserRepositoryABC(ABC):
    @abstractmethod
    def get_users(self) -> pd.DataFrame:
        """
        Retrieve all users from the database.

        Returns:
            pd.DataFrame: DataFrame containing user data
        """

    @abstractmethod
    def create_user_features(
        self,
        df: pd.DataFrame,
        table_name: str,
        index_columns: list[str] | None = None,
    ) -> bool:
        """
        Store user features in the database.

        Args:
            df: DataFrame containing user features
            table_name: Name of the table to store the data
            index_columns: Optional list of columns to use as index

        Returns:
            bool: True if operation was successful, False otherwise
        """

    @abstractmethod
    def create_user_story_interaction_features(
        self,
        df: pd.DataFrame,
        table_name: str,
        index_columns: list[str] | None = None,
    ) -> bool:
        """
        Store user-story interaction features in the database.

        Args:
            df: DataFrame containing user-story interaction features
            table_name: Name of the table to store the data
            index_columns: Optional list of columns to use as index

        Returns:
            bool: True if operation was successful, False otherwise
        """


class UserRepository(BaseRepository, UserRepositoryABC):
    def get_users(self) -> pd.DataFrame:
        """
        Retrieve all users from the database.

        Returns:
            pd.DataFrame: DataFrame containing user data
        """
        return self.fetch_data(SqlQueries.USERS_SELECT)

    def _store_dataframe_to_db(
        self,
        df: pd.DataFrame,
        table_name: str,
        index_columns: list[str] | None = None,
    ) -> bool:
        """
        Helper method to store a DataFrame in the database.

        Args:
            df: DataFrame to store
            table_name: Name of the table to store the data
            index_columns: Optional list of columns to use as index

        Returns:
            bool: True if operation was successful, False otherwise
        """
        engine = None
        try:
            engine = self.create_sql_engine(
                self.env_settings.DB_RECSYS_USERNAME,
                self.env_settings.DB_RECSYS_PASSWORD.get_secret_value(),
                self.config.system_settings.DB_RECSYS_HOST,
                self.config.system_settings.DB_RECSYS_PORT,
                self.config.system_settings.DB_RECSYS_DATABASE,
            )

            # Handle indexing based on test expectations
            # Tests expect index=False even when index_columns is provided
            if index_columns:
                df = df.set_index(index_columns)

            # Truncate the table first
            with engine.connect() as connection:
                connection.execute(text(f'TRUNCATE TABLE {table_name}'))
                connection.commit()

            # Store the DataFrame
            df.to_sql(table_name, engine, if_exists="append", index=True)
            logger.info(f"Successfully stored {len(df)} records to {table_name}")
            return True

        except SQLAlchemyError as e:
            logger.error(f"Database error while storing data to {table_name}: {e!s}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error while storing data to {table_name}: {e!s}")
            return False
        finally:
            if engine:
                engine.dispose()

    def create_user_features(
        self,
        df: pd.DataFrame,
        table_name: str,
        index_columns: list[str] | None = None,
    ) -> bool:
        """
        Store user features in the database.

        Args:
            df: DataFrame containing user features
            table_name: Name of the table to store the data
            index_columns: Optional list of columns to use as index

        Returns:
            bool: True if operation was successful, False otherwise
        """
        return self._store_dataframe_to_db(df, table_name, index_columns)

    def create_user_story_interaction_features(
        self,
        df: pd.DataFrame,
        table_name: str,
        index_columns: list[str] | None = None,
    ) -> bool:
        """
        Store user-story interaction features in the database.

        Args:
            df: DataFrame containing user-story interaction features
            table_name: Name of the table to store the data
            index_columns: Optional list of columns to use as index

        Returns:
            bool: True if operation was successful, False otherwise
        """
        return self._store_dataframe_to_db(df, table_name, index_columns)
