from datetime import datetime
from typing import Any, Dict, Optional

import pandas as pd
from loguru import logger


class UserFeature:
    """
    A class to create and transform user features from raw user data.

    This class processes user data to create derived features such as account age,
    and performs necessary data type conversions and column transformations.
    """

    def __init__(self, config: dict[str, Any]):
        """
        Initialize the UserFeature class.

        Args:
            config: Configuration dictionary containing settings for feature creation
        """
        self.config = config

    def _validate_input(self, users: pd.DataFrame) -> bool:
        """
        Validate that the input dataframe has the required columns.

        Args:
            users: DataFrame containing user data

        Returns:
            bool: True if validation passes, raises ValueError otherwise
        """
        required_columns = ["created_at", "id"]

        missing_columns = [col for col in required_columns if col not in users.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")

        return True

    def _calculate_account_age(self, users: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate the account age in days based on the created_at column.

        Args:
            users: DataFrame containing user data with created_at column

        Returns:
            pd.DataFrame: DataFrame with account_age_days and user_age columns added
        """
        try:
            # Convert to datetime if not already
            if not pd.api.types.is_datetime64_dtype(users["created_at"]):
                users["created_at"] = pd.to_datetime(
                    users["created_at"], errors="coerce"
                )

            # Calculate account age and handle invalid dates
            now = datetime.now()
            users["account_age_days"] = (now - users["created_at"]).dt.days

            # Apply constraints: age must be between 0 and max_account_age for account_age_days
            users["account_age_days"] = users["account_age_days"].clip(
                lower=0, upper=self.config.pipeline.max_account_age
            )
            users["account_age_days"] = users["account_age_days"].fillna(0).astype(int)

            return users
        except Exception as e:
            logger.error(f"Error calculating account age: {e!s}")
            # Return original dataframe if calculation fails
            return users

    def _convert_data_types(self, users: pd.DataFrame) -> pd.DataFrame:
        """
        Convert columns to appropriate data types.

        Args:
            users: DataFrame containing user data

        Returns:
            pd.DataFrame: DataFrame with columns converted to appropriate types
        """
        try:
            # Convert ID to string
            users["id"] = users["id"].astype(str)
            return users
        except Exception as e:
            logger.error(f"Error converting data types: {e!s}")
            return users

    def _drop_unnecessary_columns(self, users: pd.DataFrame) -> pd.DataFrame:
        """
        Drop unnecessary columns from the dataframe.

        Args:
            users: DataFrame containing user data

        Returns:
            pd.DataFrame: DataFrame with unnecessary columns removed
        """
        columns_to_drop = ["intro", "is_email_verified", "type"]
        drop_columns = [col for col in columns_to_drop if col in users.columns]

        if drop_columns:
            users = users.drop(columns=drop_columns)

        return users

    def create_features(self, users: pd.DataFrame) -> pd.DataFrame:
        """
        Create features from raw user data.

        This method transforms the input user dataframe by:
        1. Validating input data
        2. Calculating account age in days
        3. Converting data types
        4. Dropping unnecessary columns

        Args:
            users: DataFrame containing raw user data

        Returns:
            pd.DataFrame: DataFrame with transformed features
        """
        logger.info(f"Starting user feature creation with input shape: {users.shape}")

        try:
            # Validate input
            self._validate_input(users)

            # Apply transformations
            users = self._calculate_account_age(users)
            users = self._convert_data_types(users)
            users = self._drop_unnecessary_columns(users)
            users.rename(columns={"id": "user_id"}, inplace=True)
            users["event_timestamp"] = datetime.now()

            logger.info(f"User feature creation completed. Output shape: {users.shape}")
            return users

        except Exception as e:
            logger.error(f"Error in user feature creation: {e!s}")
            raise
