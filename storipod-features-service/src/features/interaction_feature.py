from datetime import datetime
from typing import Dict, List, Optional, Set

import numpy as np
import pandas as pd
from loguru import logger


class InteractionFeature:
    """
    Feature engineering class for creating user-story interaction features.

    This class processes various interaction data (views, reactions, comments, saves)
    to create binary interaction features for recommendation systems.
    """

    # Column name constants
    REQUIRED_COLUMNS = {
        "saved_stories": ["user_id", "story_id"],
        "story_slides": ["id", "story_id"],
        "slide_views": ["user_id", "story_id"],
        "slide_reactions": ["user_id", "slide_id"],
        "slide_comments": ["user_id", "story_id"],
    }

    def __init__(self, config: dict | None = None):
        """
        Initialize the InteractionFeature class.

        Args:
            config: Configuration dictionary for feature engineering parameters
        """
        self.config = config or {}
        logger.info("InteractionFeature initialized")

    def _validate_dataframe(
        self, df: pd.DataFrame, df_name: str, required_columns: list[str]
    ) -> None:
        """
        Validate that a dataframe has the required columns and is not empty.

        Args:
            df: DataFrame to validate
            df_name: Name of the dataframe for error messages
            required_columns: List of required column names

        Raises:
            ValueError: If validation fails
        """
        if df.empty:
            logger.warning(f"{df_name} is empty")
            return

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(
                f"{df_name} is missing required columns: {missing_columns}"
            )

        # Check for null values in required columns
        null_counts = df[required_columns].isnull().sum()
        if null_counts.any():
            logger.warning(
                f"{df_name} has null values in required columns: {null_counts[null_counts > 0].to_dict()}"
            )

    def _get_unique_user_story_pairs(
        self, df: pd.DataFrame, columns: list[str]
    ) -> pd.DataFrame:
        """
        Extract unique user-story pairs from a dataframe.

        Args:
            df: Input dataframe
            columns: Column names to extract

        Returns:
            DataFrame with unique user-story pairs
        """
        if df.empty:
            return pd.DataFrame(columns=columns)
        return df[columns].drop_duplicates()

    def _process_reaction_interactions(
        self, slide_reactions: pd.DataFrame, story_slides: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Process slide reactions to get user-story interaction pairs.

        Args:
            slide_reactions: DataFrame with slide reaction data
            story_slides: DataFrame mapping slides to stories

        Returns:
            DataFrame with unique user-story pairs from reactions
        """
        if slide_reactions.empty or story_slides.empty:
            return pd.DataFrame(columns=["user_id", "story_id"])

        # Merge reactions with story mapping
        reactions_with_stories = slide_reactions.merge(
            story_slides[["id", "story_id"]],
            left_on="slide_id",
            right_on="id",
            how="inner",  # Changed to inner join to avoid null story_ids
        )

        if reactions_with_stories.empty:
            logger.warning("No matching stories found for slide reactions")
            return pd.DataFrame(columns=["user_id", "story_id"])

        return self._get_unique_user_story_pairs(
            reactions_with_stories, ["user_id", "story_id"]
        )

    def create_features(
        self,
        saved_stories: pd.DataFrame,
        story_slides: pd.DataFrame,
        slide_views: pd.DataFrame,
        slide_reactions: pd.DataFrame,
        slide_comments: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Create binary interaction features for user-story pairs.

        This method processes various interaction types (views, reactions, comments, saves)
        and creates a binary interaction feature indicating whether a user has interacted
        with a story through any of these channels.

        Args:
            saved_stories: DataFrame with user story saves
            story_slides: DataFrame mapping slides to stories
            slide_views: DataFrame with slide view events
            slide_reactions: DataFrame with slide reaction events
            slide_comments: DataFrame with slide comment events

        Returns:
            DataFrame with columns: user_id, story_id, is_liked, event_timestamp

        Raises:
            ValueError: If required columns are missing from input dataframes
        """
        logger.info("Starting interaction feature creation")

        try:
            # Validate input dataframes
            for df_name, df in [
                ("saved_stories", saved_stories),
                ("story_slides", story_slides),
                ("slide_views", slide_views),
                ("slide_reactions", slide_reactions),
                ("slide_comments", slide_comments),
            ]:
                self._validate_dataframe(df, df_name, self.REQUIRED_COLUMNS[df_name])

            # Extract unique user-story pairs from each interaction type
            viewed_pairs = self._get_unique_user_story_pairs(
                slide_views, ["user_id", "story_id"]
            )
            logger.info(f"Found {len(viewed_pairs)} unique view pairs")

            reaction_pairs = self._process_reaction_interactions(
                slide_reactions, story_slides
            )
            logger.info(f"Found {len(reaction_pairs)} unique reaction pairs")

            comment_pairs = self._get_unique_user_story_pairs(
                slide_comments, ["user_id", "story_id"]
            )
            logger.info(f"Found {len(comment_pairs)} unique comment pairs")

            saved_pairs = self._get_unique_user_story_pairs(
                saved_stories, ["user_id", "story_id"]
            )
            logger.info(f"Found {len(saved_pairs)} unique saved pairs")

            # Combine all interaction types (excluding views as they're used as base)
            interaction_dataframes = [
                df
                for df in [reaction_pairs, comment_pairs, saved_pairs]
                if not df.empty
            ]

            if interaction_dataframes:
                all_interactions = pd.concat(
                    interaction_dataframes, ignore_index=True
                ).drop_duplicates()
                all_interactions["is_liked"] = 1
                logger.info(f"Found {len(all_interactions)} unique interaction pairs")
            else:
                all_interactions = pd.DataFrame(
                    columns=["user_id", "story_id", "is_liked"]
                )
                logger.warning("No interaction data found")

            # Create final feature set by merging views with interactions
            if viewed_pairs.empty:
                logger.warning("No view data found, returning empty result")
                return pd.DataFrame(
                    columns=["user_id", "story_id", "is_liked", "event_timestamp"]
                )

            user_story_features = viewed_pairs.merge(
                all_interactions, on=["user_id", "story_id"], how="left"
            )

            # Fill missing interaction values and add metadata
            user_story_features["is_liked"] = (
                user_story_features["is_liked"].fillna(0).astype(int)
            )

            user_story_features["event_timestamp"] = datetime.now()

            # Ensure consistent column order
            user_story_features = user_story_features[
                ["user_id", "story_id", "is_liked", "event_timestamp"]
            ]

            logger.info(f"Created {len(user_story_features)} interaction features")
            logger.info(
                f"Interaction rate: {user_story_features['is_liked'].mean():.2%}"
            )

            return user_story_features

        except Exception as e:
            logger.error(f"Error creating interaction features: {e!s}")
            raise

    # def get_interaction_statistics(self, features_df: pd.DataFrame) -> Dict[str, any]:
    #     """
    #     Get statistics about the interaction features.

    #     Args:
    #         features_df: DataFrame returned by create_features()

    #     Returns:
    #         Dictionary with interaction statistics
    #     """
    #     if features_df.empty:
    #         return {'total_pairs': 0, 'interaction_rate': 0, 'unique_users': 0, 'unique_stories': 0}

    #     stats = {
    #         'total_pairs': len(features_df),
    #         'interaction_rate': features_df['is_liked'].mean(),
    #         'total_interactions': features_df['is_liked'].sum(),
    #         'unique_users': features_df['user_id'].nunique(),
    #         'unique_stories': features_df['story_id'].nunique(),
    #     }

    #     logger.info(f"Interaction statistics: {stats}")
    #     return stats
