import re
from datetime import datetime
from typing import List

import chromadb
import pandas as pd
from chromadb.config import Settings
from loguru import logger
from openai import AzureOpenAI
from sqlalchemy import create_engine
from src.utils.sql_queries import SqlQueries

class EmbeddingFeature:
    def __init__(self, env_settings, config):
        self.env_settings = env_settings
        self.config = config

    def add_embedding(
        self, df: pd.DataFrame, collection_name: str, id_column: str, text_column: str
    ):
        logger.info("Starting add_embedding_features method.")

        if id_column not in df.columns or text_column not in df.columns:
            raise ValueError(
                f"DataFrame must contain columns `{id_column}` and `{text_column}`."
            )
        if df[id_column].isnull().any() or df[text_column].isnull().any():
            raise ValueError("Columns must not contain null values.")

        try:
            client = chromadb.HttpClient(
                host=self.env_settings.CHROMADB_HOST,
                port=self.env_settings.CHROMADB_PORT,
                settings=Settings(allow_reset=self.env_settings.CHROMADB_ALLOW_RESET),
            )

            collection = client.get_or_create_collection(name=collection_name)
            batch_size = 500  # TODO: Add to config
            checkpoint_data = []

            for start in range(0, len(df), batch_size):
                logger.info(
                    f"Adding embeddings for rows {start} to {start + batch_size}"
                )
                end = start + batch_size
                batch_df = df.iloc[start:end]

                # Get max date in this batch
                if "created_at" in batch_df.columns:
                    max_date_in_batch = batch_df["created_at"].max()
                    checkpoint_data.append(
                        {
                            "collection_name": collection_name,
                            "batch_start": start,
                            "batch_end": end,
                            "max_date": max_date_in_batch,
                            "processed_at": datetime.now(),
                        }
                    )
                    logger.info(f"Batch {start}-{end}: Max date = {max_date_in_batch}")

                collection.add(
                    ids=batch_df[id_column].values.tolist(),
                    documents=batch_df[text_column].values.tolist(),
                )

            self._save_checkpoint_data(checkpoint_data)

        except Exception as e:
            logger.error(f"Failed to add embedding features: {e}")

        logger.info("Completed add_embedding_features method.")

    def get_max_created_timestamp(self, collection_name: str) -> str:
        """
        Get the maximum created timestamp from the checkpoint table for a collection.

        Args:
            collection_name (str): Name of the collection

        Returns:
            str: Maximum created timestamp as string, or None if no checkpoint exists
        """
        engine = None
        try:
            engine = self._create_sql_engine()
            result = pd.read_sql_query(SqlQueries.GET_EMBEDDING_CHECKPOINT_BY_COLLECTION_NAME.format(collection_name), engine)

            if result.empty or result["max_date"].iloc[0] is None:
                logger.info(f"No checkpoint found for collection: {collection_name}")
                return None

            max_date = result["max_date"].iloc[0]
            logger.info(f"Found max date for {collection_name}: {max_date}")
            return max_date

        except Exception as e:
            logger.error(f"Error getting max created timestamp: {e}")
            return None
        finally:
            if engine:
                engine.dispose()

    def _save_checkpoint_data(self, checkpoint_data: list[dict]):
        """
        Save checkpoint data to the database.

        Args:
            checkpoint_data (List[dict]): List of checkpoint records to save
        """
        engine = None
        try:
            engine = self._create_sql_engine()
            checkpoint_df = pd.DataFrame(checkpoint_data)

            # Insert checkpoint data
            checkpoint_df.to_sql(
                "embedding_checkpoints", engine, if_exists="append", index=False
            )
            logger.info(f"Saved {len(checkpoint_data)} checkpoint records")

        except Exception as e:
            logger.error(f"Error saving checkpoint data: {e}")
        finally:
            if engine:
                engine.dispose()

    def _create_sql_engine(self):
        """
        Create SQLAlchemy engine for database operations.

        Returns:
            SQLAlchemy engine
        """
        connection_string = (
            f"postgresql://{self.env_settings.DB_RECSYS_USERNAME}:"
            f"{self.env_settings.DB_RECSYS_PASSWORD.get_secret_value()}@"
            f"{self.env_settings.DB_RECSYS_HOST}:"
            f"{self.env_settings.DB_RECSYS_PORT}/"
            f"{self.env_settings.DB_RECSYS_DATABASE}"
        )
        return create_engine(connection_string)

    def get_embedding(
        self, collection_name: str, id_column: str, query_text: str, num_results: int
    ) -> pd.DataFrame:
        client = chromadb.HttpClient(
            host=self.env_settings.CHROMADB_HOST,
            port=self.env_settings.CHROMADB_PORT,
            settings=Settings(allow_reset=self.env_settings.CHROMADB_ALLOW_RESET),
        )
        collection = client.get_collection(name=collection_name)

        results = collection.query(query_texts=[query_text], n_results=num_results)

        df_results = pd.DataFrame(
            {id_column: results[id_column][0], "distance": results["distances"][0]}
        )

        return df_results

    def clean_text(self, text):
        text = re.sub(r"[^\w\s.,!?']", " ", text)
        text = re.sub(r"\s+", " ", text)
        return text.strip()

    def _extract_substrings(
        self, dataset: pd.DataFrame, min_length: int = 1000, max_length: int = 2000
    ) -> list[str]:
        extracts = []
        sentence_pattern = r"(?<!\w\.\w.)(?<![A-Z][a-z]\.)(?<=\.|\?|\!)\s"
        for article in dataset["content"]:
            cleaned_article = self._clean_text(article)
            sentences = re.split(sentence_pattern, cleaned_article)
            current_chunk = ""
            for sentence in sentences:
                sentence = sentence.strip()
                if not sentence:
                    continue
                if len(current_chunk) + len(sentence) <= max_length:
                    current_chunk += sentence + " "
                else:
                    if len(current_chunk) >= min_length:
                        extracts.append(current_chunk.strip())
                    current_chunk = sentence + " "
            if len(current_chunk) >= min_length:
                extracts.append(current_chunk.strip())
        return extracts

    def summarize_text(self, text: str) -> str:
        client = AzureOpenAI(
            api_version=self.env_settings.OPENAI_API_VERSION,
            azure_endpoint=self.env_settings.AZURE_OPENAI_ENDPOINT,
            api_key=self.env_settings.OPENAI_API_KEY.get_secret_value(),
        )

        try:
            if len(text) < 2000:
                return text

            prompt = f"""Summarize the following text in 700 words or less, maintaining the key points and main ideas:

            {text}

            Summary:"""

            # Generate the summary using OpenAI
            response = client.chat.completions.create(
                model=self.env_settings.OPENAI_MODEL,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a helpful assistant that summarizes text concisely while maintaining key information.",
                    },
                    {"role": "user", "content": prompt},
                ],
                temperature=0.3,  # Lower temperature for more focused summaries
                max_tokens=1000,  # Limit response length
            )

            summary = response.choices[0].message.content.strip()
            if not summary:
                logger.warning("Generated summary is empty, returning original text")
                return text

            return summary

        except Exception as e:
            logger.error(f"Failed to summarize text: {e}")
            return text
