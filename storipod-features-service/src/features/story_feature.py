from datetime import datetime
from typing import Any, Dict

import pandas as pd
from loguru import logger


class StoryFeature:
    # Define class constants for rating weights

    def __init__(self, config: dict[str, Any]):
        """
        Initialize StoryFeature with configuration.

        Args:
            config: Configuration dictionary for feature processing
        """
        self.config = config

    def _process_story_basics(self, stories: pd.DataFrame) -> pd.DataFrame:
        """
        Process basic story features common to multiple methods.

        This helper method handles common data processing steps for stories DataFrame.

        Args:
            stories: DataFrame containing story data

        Returns:
            Processed DataFrame with basic features
        """
        stories = stories.copy()
        stories["created_at"] = pd.to_datetime(stories["created_at"]).dt.tz_localize(
            None
        )
        stories["story_age"] = (pd.Timestamp.now() - stories["created_at"]).dt.days
        stories["story_age"] = stories["story_age"].astype(int)
        return stories

    def _add_story_views_count(
        self,
        stories: pd.DataFrame,
        slide_views: pd.DataFrame,
        id_column: str = "story_id",
    ) -> pd.DataFrame:
        """
        Add slide_views count feature to stories DataFrame.

        Args:
            stories: DataFrame containing story data
            saved_stories: DataFrame containing slide_views story data
            id_column: Column to use for mapping slide_views counts (default: 'story_id')

        Returns:
            DataFrame with views_count feature added
        """
        views_count = slide_views.groupby("story_id").size().reset_index(name="counts")

        stories["views_count"] = stories[id_column].map(
            views_count.set_index("story_id")["counts"]
        )

        stories["views_count"] = stories["views_count"].fillna(0).astype(int)
        return stories

    def _add_story_comments_count(
        self,
        stories: pd.DataFrame,
        slide_comments: pd.DataFrame,
        id_column: str = "story_id",
    ) -> pd.DataFrame:
        """
        Add slide_comments count feature to stories DataFrame.

        Args:
            stories: DataFrame containing story data
            saved_stories: DataFrame containing slide_comments story data
            id_column: Column to use for mapping slide_comments counts (default: 'story_id')

        Returns:
            DataFrame with slide_comments feature added
        """
        comments_count = (
            slide_comments.groupby("story_id").size().reset_index(name="counts")
        )

        stories["comments_count"] = stories[id_column].map(
            comments_count.set_index("story_id")["counts"]
        )

        stories["comments_count"] = stories["comments_count"].fillna(0).astype(int)
        return stories

    def _add_engagement_ratio(
        self,
        stories: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Add engagement ratio feature to stories DataFrame.

        Args:
            stories: DataFrame containing story data
            slide_comments: DataFrame containing slide_comments data
            id_column: Column to use for mapping engagement ratios (default: 'story_id')
        """

        stories.loc[:, "engagement_ratio"] = (
            stories["comments_count"] / stories["views_count"]
        ).fillna(0)

        return stories

    def _add_saved_count(
        self,
        stories: pd.DataFrame,
        saved_stories: pd.DataFrame,
        id_column: str = "story_id",
    ) -> pd.DataFrame:
        """
        Add saved count feature to stories DataFrame.

        Args:
            stories: DataFrame containing story data
            saved_stories: DataFrame containing saved story data
            id_column: Column to use for mapping saved counts (default: 'story_id')

        Returns:
            DataFrame with saved_count feature added
        """
        saved_stories_count = (
            saved_stories.groupby("story_id").size().reset_index(name="counts")
        )

        stories["saved_count"] = stories[id_column].map(
            saved_stories_count.set_index("story_id")["counts"]
        )

        stories["saved_count"] = stories["saved_count"].fillna(0).astype(int)
        return stories

    def create_top_stories_features(
        self, stories: pd.DataFrame, saved_stories: pd.DataFrame
    ) -> pd.DataFrame:
        """
        Create features for top stories.

        This method processes the input DataFrame of stories and generates additional
        features such as 'story_age' and 'saved_count'. The 'story_age' feature
        represents the age of the story in days, and the 'saved_count' feature
        represents the number of times the story has been saved.

        Parameters:
        -----------
        stories : pd.DataFrame
            DataFrame containing story data. Must include columns 'id', 'user_id',
            and 'created_at'.
        saved_stories : pd.DataFrame
            DataFrame containing saved story data. Must include column 'story_id'.

        Returns:
        --------
        pd.DataFrame
            DataFrame with additional features 'story_age' and 'saved_count'.
        """
        logger.info(f"Processing {len(stories)} stories for top stories features")

        stories = stories.copy()
        stories["id"] = stories["id"].astype(str)
        stories["user_id"] = stories["user_id"].astype(str)

        # Process basic story features
        stories = self._process_story_basics(stories)

        # Add saved count
        stories = self._add_saved_count(stories, saved_stories, id_column="id")

        logger.info(f"Completed top stories feature processing. Shape: {stories.shape}")
        return stories

    def create_features(
        self,
        users: pd.DataFrame,
        stories: pd.DataFrame,
        saved_stories: pd.DataFrame,
        story_slides: pd.DataFrame,
        slide_views: pd.DataFrame,
        slide_comments: pd.DataFrame,
    ) -> pd.DataFrame:
        """
        Create features for stories by combining user, story, saved story,
        and slide data.

        Args:
            users (pd.DataFrame): DataFrame with user information ('id' and 'email')
            stories (pd.DataFrame): DataFrame with story information ('id', 'user_id',
                and 'created_at')
            saved_stories (pd.DataFrame): DataFrame with saved story information
                ('story_id')
            story_slides (pd.DataFrame): DataFrame with story slide information
                ('story_id', 'user_id', 'title', and 'content')

        Returns:
            pd.DataFrame: DataFrame with combined and transformed features for stories
        """
        logger.info(
            f"Creating features from {len(stories)} stories and "
            f"{len(story_slides)} slides"
        )

        stories = stories.copy()
        stories["story_id"] = stories["id"].astype(str)
        stories.drop(columns=["id"], inplace=True)
        stories["user_id"] = stories["user_id"].astype(str)

        users["id"] = users["id"].astype(str)
        saved_stories["story_id"] = saved_stories["story_id"].astype(str)
        story_slides["story_id"] = story_slides["story_id"].astype(str)
        slide_views["story_id"] = slide_views["story_id"].astype(str)
        slide_comments["story_id"] = slide_comments["story_id"].astype(str)

        # Process basic story features
        stories = self._process_story_basics(stories)

        # Add saved count using story_id
        stories = self._add_saved_count(stories, saved_stories)

        # Add story views count
        stories = self._add_story_views_count(stories, slide_views)

        # Add story comments count
        stories = self._add_story_comments_count(stories, slide_comments)

        # set views_count to 1 if views_count is 0 and comments_count > 0
        stories.loc[:, "views_count"] = stories["views_count"].where(
            ~((stories["views_count"] == 0) & (stories["comments_count"] > 0)), 1
        )

        # Add story comments count
        stories = self._add_engagement_ratio(stories)

        # Merge with user data to get author information
        stories = stories.merge(
            users[["id", "email"]], left_on="user_id", right_on="id", how="inner"
        )
        stories.drop(columns=["user_id", "id"], inplace=True)
        stories.rename(columns={"email": "author"}, inplace=True)

        # Fill None values in content and title columns before groupby aggregation
        story_slides = story_slides.copy()
        story_slides["content"] = story_slides["content"].fillna("")
        story_slides["title"] = story_slides["title"].fillna("")

        # Combine slides by title first
        logger.info("Combining story slides by title")
        combined_story_slides = story_slides.groupby(
            ["story_id", "user_id", "title"], as_index=False
        ).agg({"content": " ".join})

        combined_story_slides["content"] = (
            combined_story_slides["title"] + ". " + combined_story_slides["content"]
        )
        combined_story_slides.drop(columns=["title"], inplace=True)
        logger.info(f"Combined story slides shape: {combined_story_slides.shape}")

        # Then combine all slides for each story
        logger.info("Combining all slides by story")
        combined_stories = combined_story_slides.groupby(
            ["story_id", "user_id"], as_index=False
        ).agg({"content": " ".join})
        combined_stories["content"] = combined_stories["content"].fillna("")
        logger.info(f"Combined stories shape: {combined_stories.shape}")

        # Ensure story_id is string type for proper merging
        combined_stories["story_id"] = combined_stories["story_id"].astype(str)
        stories["story_id"] = stories["story_id"].astype(str)

        # Merge stories with combined content
        stories = stories.merge(combined_stories, on="story_id", how="inner")

        # Add content length
        stories["content_length"] = stories["content"].apply(len)
        stories["content_length"] = stories["content_length"].fillna(0).astype(int)

        columns_to_drop = ["user_id", "title"]
        stories.drop(columns=columns_to_drop, inplace=True)
        stories["event_timestamp"] = datetime.now()

        logger.info(f"Final stories feature shape: {stories.shape}")
        logger.info("Data transformation completed.")

        return stories

    def compute_average_rating(self, story_features: pd.DataFrame) -> pd.DataFrame:
        """
        Compute the average rating for stories and filter out qualified stories.

        This method calculates the average rating for each story based on the reactions
        count, comments count, saved count, and total views count. It then computes
        the mean rating and the 90th percentile of total views count to filter out
        the top stories.

        Args:
            story_features (pd.DataFrame): A DataFrame containing story features with
                columns 'reactions_count', 'comments_count', 'saved_count',
                and 'total_views_count'

        Returns:
            pd.DataFrame: A DataFrame containing the qualified stories with additional
                columns 'rating', 'C' (mean rating), and 'm' (90th percentile
                of total views count)
        """
        logger.info(f"Computing average ratings for {len(story_features)} stories")

        # Compute average rating
        story_features["rating"] = story_features.apply(
            lambda x: self._calculate_rating(
                x["reactions_count"],
                x["comments_count"],
                x["saved_count"],
                x["total_views_count"],
            ),
            axis=1,
        )

        # Calculate mean of rating column
        C = story_features["rating"].mean()
        logger.info(f"Mean rating (C): {C:.4f}")

        # Calculate the minimum number of views required to be on the list, m
        m = story_features["total_views_count"].quantile(0.90)
        logger.info(f"View threshold for qualification (m): {m:.1f}")

        # Filter out all qualified stories into a new DataFrame
        qualified_stories = story_features.copy().loc[
            story_features["total_views_count"] >= m
        ]
        qualified_stories["C"] = C
        qualified_stories["m"] = m

        logger.info(
            f"Found {len(qualified_stories)} qualified stories out of "
            f"{len(story_features)}"
        )
        return qualified_stories

    def _calculate_rating(
        self, likes: int, comments: int, saves: int, views: int
    ) -> float:
        """
        Calculate the rating of a story based on likes, comments, saves, and views.

        The rating is calculated using the following weights:
        - Likes: 0.3 (default) or config.pipeline.top_stories_like_weight
        - Comments: 0.5 (default) or config.pipeline.top_stories_comment_weight
        - Saves: 0.2 (default) or config.pipeline.top_stories_save_weight

        The formula used is:
        rating = (like_weight * (likes / views)) +
                (comment_weight * (comments / views)) +
                (save_weight * (saves / views))

        Args:
            likes (int): The number of likes the story has received
            comments (int): The number of comments the story has received
            saves (int): The number of saves the story has received
            views (int): The number of views the story has received

        Returns:
            float: The calculated rating of the story. Returns 0.0 if views are 0
        """
        if views == 0:
            return 0.0

        # Use config weights if available, otherwise use defaults
        try:
            like_weight = self.config.pipeline.top_stories_like_weight
            comment_weight = self.config.pipeline.top_stories_comment_weight
            save_weight = self.config.pipeline.top_stories_save_weight
        except AttributeError:
            # Default weights if config attributes don't exist
            like_weight = 0.3
            comment_weight = 0.5
            save_weight = 0.2

        return (
            (like_weight * (likes / views))
            + (comment_weight * (comments / views))
            + (save_weight * (saves / views))
        )

    def _weighted_rating(self, x: pd.Series) -> float:
        """
        Calculate the weighted rating for a given item.

        The weighted rating is calculated using the formula:
        (v / (v + m) * R) + (m / (m + v) * C)

        where:
        - v is the total number of views for the item
        - R is the average rating of the item
        - m is the minimum number of views required to be listed in the chart
        - C is the mean rating across all items

        Args:
            x (pd.Series): A pandas Series containing the keys 'total_views_count',
                'rating', 'm', and 'C'

        Returns:
            float: The weighted rating of the item. Returns 0.0 if views are 0
        """
        v = x["total_views_count"]
        R = x["rating"]
        m = x["m"]
        C = x["C"]

        if v == 0:
            return 0.0

        return (v / (v + m) * R) + (m / (m + v) * C)

    def compute_weighted_rating(self, qualified_stories: pd.DataFrame) -> pd.DataFrame:
        """
        Compute the weighted rating for qualified stories.

        This method calculates a weighted rating score for each story in the
        qualified_stories DataFrame by applying the _weighted_rating function.
        It then rounds the 'score' and 'rating' columns to 5 decimal places,
        sorts the stories by the 'score' in descending order, and logs the
        completion of the data transformation.

        Args:
            qualified_stories (pd.DataFrame): A DataFrame containing the qualified
                stories with at least 'id', 'user_id', and 'rating' columns

        Returns:
            pd.DataFrame: A DataFrame with the 'id', 'user_id', 'rating', and 'score'
                columns, sorted by 'score' in descending order
        """
        logger.info(f"Computing weighted ratings for {len(qualified_stories)} stories")

        # Apply weighted rating to each row
        qualified_stories["score"] = qualified_stories.apply(
            self._weighted_rating, axis=1
        )

        # Keep only necessary columns
        result = qualified_stories[["id", "user_id", "rating", "score"]].copy()

        # Round values for better display
        result.loc[:, "score"] = result["score"].round(5)
        result.loc[:, "rating"] = result["rating"].round(5)

        # Sort by score in descending order
        result = result.sort_values("score", ascending=False)

        top_score = result["score"].max() if not result.empty else 0.0
        logger.info(f"Weighted rating computation complete. Top score: {top_score:.5f}")
        return result
