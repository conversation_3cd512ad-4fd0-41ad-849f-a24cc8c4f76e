from pathlib import Path
import os

import yaml
from dotenv import find_dotenv, load_dotenv
from pydantic import BaseModel, SecretStr
from pydantic_settings import BaseSettings

dotenv_path = find_dotenv()
load_dotenv(dotenv_path)

PACKAGE_ROOT = Path(__file__).resolve().parent
ROOT = PACKAGE_ROOT.parent
CONFIG_FILE_PATH = ROOT.parents[0] / "config.yml"


def _load_secrets_from_key_vault_if_configured() -> None:
    """
    Load secrets from Azure Key Vault into environment variables if a vault is configured.

    Configuration options (one of these must be set):
    - AZURE_KEY_VAULT_URL: Full vault URL, e.g. https://<name>.vault.azure.net
    - AZURE_KEY_VAULT_NAME: Vault name, the URL will be derived as https://<name>.vault.azure.net

    Authentication: Relies on DefaultAzureCredential chain (Managed Identity, Service Principal, Azure CLI, etc.).
    """
    key_vault_url = os.getenv("AZURE_KEY_VAULT_URL")
    if not key_vault_url:
        key_vault_name = os.getenv("AZURE_KEY_VAULT_NAME")
        if key_vault_name:
            key_vault_url = f"https://{key_vault_name}.vault.azure.net"

    if not key_vault_url:
        # Key Vault is not configured; skip silently
        return

    try:
        from azure.identity import DefaultAzureCredential
        from azure.keyvault.secrets import SecretClient
    except Exception:
        # Azure SDK not installed; skip silently so local .env-based dev still works
        return

    try:
        credential = DefaultAzureCredential()
        client = SecretClient(vault_url=key_vault_url, credential=credential)
    except Exception:
        # Unable to create client (e.g., no auth available); skip
        return

    secret_names = [
        "DB_SOURCE_USERNAME",
        "DB_SOURCE_PASSWORD",
        "DB_RECSYS_USERNAME",
        "DB_RECSYS_PASSWORD",
        "AZURE_BLOB_ACCESS_KEY",
        "OPENAI_API_KEY",
        "REDIS_CONNECTION_STRING",
    ]

    for secret_name in secret_names:
        try:
            # Do not override an already-present environment variable
            if os.getenv(secret_name):
                continue

            secret = client.get_secret(secret_name)
            if secret and secret.value:
                os.environ[secret_name] = secret.value
        except Exception:
            # If a specific secret is missing or forbidden, continue with others
            continue


class EnvSettings(BaseSettings):
    class Config:
        env_file = dotenv_path
        case_sensitive = True

    
    DB_SOURCE_USERNAME: str
    DB_SOURCE_PASSWORD: SecretStr
    DB_RECSYS_USERNAME: str
    DB_RECSYS_PASSWORD: SecretStr
    AZURE_BLOB_ACCOUNT_NAME: str
    AZURE_BLOB_ACCESS_KEY: SecretStr
    OPENAI_API_KEY: SecretStr
    REDIS_CONNECTION_STRING: str


# class FeatureStoreConfig(BaseModel):
#     UserFeatureGroupName: str
#     StoryFeatureGroupName: str


class PipelineConfig(BaseModel):
    class Config:
        extra = "allow"

    user_features_table: str
    story_features_table: str
    interaction_features_table: str
    story_window_length: int
    max_account_age: int
    enable_summarization: bool


class SystemSettings(BaseModel):
    class Config:
        extra = "allow"

    ENV: str
    DB_SOURCE_HOST: str
    DB_SOURCE_PORT: int
    DB_SOURCE_DATABASE: str
    DB_RECSYS_HOST: str
    DB_RECSYS_PORT: int
    DB_RECSYS_DATABASE: str
    CHROMADB_HOST: str
    CHROMADB_PORT: int
    CHROMADB_ALLOW_RESET: bool
    AZURE_OPENAI_ENDPOINT: str
    OPENAI_MODEL: str
    OPENAI_API_VERSION: str


class AppConfig(BaseModel):
    class Config:
        extra = "allow"

    pipeline: PipelineConfig
    system_settings: SystemSettings


def load_config_from_yaml(config_path: str) -> AppConfig:
    with open(config_path) as file:
        config_dict = yaml.safe_load(file)
    return AppConfig(**config_dict)


# Load secrets from Azure Key Vault (if configured) before initializing settings
#_load_secrets_from_key_vault_if_configured()

env_settings = EnvSettings()
app_config = load_config_from_yaml(CONFIG_FILE_PATH)


print(env_settings)
print(app_config.system_settings.DB_SOURCE_HOST)
##print(app_config.feature_store.UserFeatureGroupName)
