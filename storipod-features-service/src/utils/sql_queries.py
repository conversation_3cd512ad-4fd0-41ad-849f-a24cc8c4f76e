class SqlQueries:
    # USERS
    USERS_SELECT = """SELECT id, intro, email, is_email_verified,created_at, updated_at,followers_count, followings_count FROM users WHERE is_email_verified = true and deleted_at is null"""
    GET_STORIES_AGGREGATE = """SELECT id, user_id, is_published,comments_count, title,
    total_views_count, reactions_count, created_at 
    FROM stories WHERE is_published = true
    AND deleted_at is null
    AND created_at >= '{}'
    AND quality in ('HIGH_QUALITY','ACCEPTABLE')
    """

    GET_SAVED_STORIES_BY_CREATED_AT = (
        """SELECT id, user_id, story_id, created_at, updated_at FROM saved_stories WHERE created_at >= '{}' """
    )

    GET_STORIES = """
    SELECT s.id, s.user_id, s.title, s.created_at, u.followers_count as author_followers_count, u.followings_count as author_followings_count 
    FROM stories s
    left join users u on s.user_id = u.id
    WHERE s.is_published = true
    AND s.deleted_at is null
    --AND s.created_at >= '{}'
    AND s.quality in ('HIGH_QUALITY','ACCEPTABLE')
    """

    GET_READ_STORY_IDS = """
                        SELECT sv.user_id, sv.story_id FROM slide_views sv
                        JOIN stories s
                        ON sv.story_id = s.id
                        WHERE sv.user_id in ('{}')
                        AND s.is_published = true
                        AND s.deleted_at is null
                        group by sv.user_id, sv.story_id
    """

    GET_SAVED_STORIES = (
        """SELECT id, user_id, story_id, created_at, updated_at FROM saved_stories"""
    )
    GET_STORY_SLIDES_AGGREGATE = """
                        SELECT story_slides.id, stories.title, story_slides.title, content, 
                        story_id, stories.user_id,story_slides.comments_count, 
                        story_slides.reactions_count, story_slides.views_count
                        FROM public.story_slides
                        inner join stories on stories.id = story_slides.story_id
                        WHERE story_slides.is_visible = true
                        AND story_slides.deleted_at  is null
                        AND stories.is_published = true
                        AND stories.deleted_at is null
                        """
    GET_STORY_SLIDES = """ 
                        SELECT id, content, story_id, user_id, title
                        FROM public.story_slides
                        WHERE story_slides.is_visible = true
                        AND story_slides.deleted_at  is null
                        """
    GET_STORY_SLIDES_BY_STORY_CREATED_AT = """ 
                        SELECT id, content, story_id, user_id, title
                        FROM public.story_slides
                        WHERE story_slides.is_visible = true
                        AND story_slides.deleted_at  is null
                        AND created_at > '{}'
                        """

    GET_SLIDE_VIEWS = """SELECT id, story_id, user_id, slide_id,created_at,duration FROM slide_views"""
    GET_SLIDE_REACTIONS = """SELECT id, slide_id, reaction_id, user_id,created_at, updated_at FROM slide_reactions"""
    GET_SLIDE_COMMENTS = """SELECT id, story_id, user_id, slide_id, content, parent_comment_id, created_at, updated_at FROM slide_comments"""
    GET_USER_PREFERRED_CATEGORIES = (
        """SELECT user_id, category_id FROM preferred_categories"""
    )
    GET_USER_PREFERRED_CATEGORIES_BY_USER_ID = """ SELECT user_id,category_id FROM preferred_categories WHERE user_id = '{}' """

    GET_STORIES_BY_LAST_CREATION_TIMESTAMP = """SELECT id, user_id, is_published, title, created_at FROM stories WHERE is_published = true AND deleted_at is null AND date_trunc('second', created_at AT TIME ZONE 'UTC') > '{}' """
    GET_STORY_CHECKPOINT = """SELECT created_at FROM story_checkpoint"""
    # user_features_select = """SELECT id, intro, is_active,created_at, updated_at,followers_count, followings_count, user_type FROM users"""
    # story_features_select = """SELECT id, user_id, is_published, comments_count, total_views_count, reactions_count, created_at, story_age, saved_count FROM story_fts"""
    GET_STORY_CATEGORIES = """ select category_id, story_id from story_categories """

    GET_FTS_USER_STORY_INTERACTIONS = """SELECT * FROM fts_user_story_interactions"""
    GET_FTS_STORIES = """SELECT * FROM fts_stories"""
    GET_FTS_STORIES_MAX_DATE = """SELECT MAX(created_at) as max_date FROM fts_stories"""

    GET_STORY_FEATURES = """SELECT * FROM fts_stories WHERE created_at > '{}' """

    GET_EMBEDDING_CHECKPOINT_BY_COLLECTION_NAME = """
            SELECT MAX(max_date) as max_date 
            FROM embedding_checkpoints 
            WHERE collection_name = '{}' """

    GET_EMBEDDING_CHECKPOINT_MAX_DATE = """
            SELECT MAX(max_date) as max_date 
            FROM embedding_checkpoints 
            """