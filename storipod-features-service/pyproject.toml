[project]
name = "storipod-features-service"
version = "0.1.0"
description = "Feature engineering service for Storipod recommendation system"
readme = "README.md"
requires-python = "~=3.12"
dependencies = [
    # Core data processing
    "pandas>=2.1.0",
    "numpy<2.0.0",
    "pyarrow==14.0.2",
    "scipy>=1.11.0",
    "scikit-learn",
    # Database and ORM
    "SQLAlchemy",
    "psycopg2-binary==2.9.10",
    "psycopg-binary==3.2.9",
    "psycopg==3.2.9",
    "psycopg-pool==3.2.6",
    # Configuration and validation
    "pydantic>=2.10.1",
    "pydantic-settings>=2.6.1",
    "python-dotenv>=1.0.1",
    "pyyaml==6.0.2",
    # Logging and monitoring
    "loguru>=0.7.2",
    # Cloud services
    "azure-storage-blob",
    "openai>=1.87.0",
    # Feature store and ML
    "chromadb==1.0.0",
    "sentence_transformers",
    # Data quality and profiling
    "ydata-profiling",
    "evidently",
    # Utilities
    "joblib",
    "genCode==1.0.1",
    "protobuf>=3.20.0,<4.0.0",
    "redis",
    "azure-identity>=1.16.0",
    "azure-keyvault-secrets>=4.8.0",
]

[dependency-groups]
dev = [
    # Testing
    "pytest>=8.3.3",
    "pytest-cov",
    "pytest-mock",
    "pytest-asyncio>=0.21.0",
    "factory-boy>=3.3.0",
    "faker>=20.0.0",
    
    # Code quality
    "ruff>=0.7.2",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.7.0",
    "pre-commit>=3.5.0",
    "bandit>=1.7.5",
    "safety>=2.3.0",
]

[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = "test_*.py"
python_classes = "Test*"
python_functions = "test_*"
addopts = "--verbose --cov=src --cov-report=term --cov-report=xml:coverage.xml"
filterwarnings = [
    "ignore::DeprecationWarning",
    "ignore::UserWarning",
]

[tool.ruff]
line-length = 88
target-version = "py312"

[tool.ruff.lint]
#select = ["E", "F", "W", "I", "N", "UP", "B", "C4", "SIM", "ARG", "PIE", "T20", "Q", "RSE", "RET", "SLF", "SLOT", "TID", "INT", "PTH", "ERA", "PD", "PGH", "PL", "TRY", "NPY", "AIR", "PERF", "FURB", "LOG", "RUF"]
ignore = ["F401", "E501"]

[tool.ruff.format]

[tool.black]
line-length = 88
target-version = ['py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
known_third_party = ["pandas", "numpy", "loguru", "pydantic", "sqlalchemy"]

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pandas.*",
    "numpy.*",
    "scipy.*",
    "sklearn.*",
    "evidently.*",
    "feast.*",
    "chromadb.*",
    "sentence_transformers.*",
    "azure.*",
    "openai.*",
    "redis.*",
]
ignore_missing_imports = true

[tool.bandit]
exclude_dirs = ["tests", "test_*"]
skips = ["B101", "B601"]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
