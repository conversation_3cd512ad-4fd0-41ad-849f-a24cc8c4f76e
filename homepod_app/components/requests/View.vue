<template>
    <main>
        <RequestsDashboardStats :totalAds="totalAds" :adType="adTypeLabel" :advertType="advertType" />
        <RequestsTable :ads="ads" :loading="loading" :pagination="pagination" :searchTerm="queryObj.searchTerm"
            :sortKey="queryObj.sortBy" :sortOrder="queryObj.orderBy" @update:searchTerm="onSearchTermUpdate"
            @update:sortKey="onSortKeyUpdate" @update:sortOrder="onSortOrderUpdate" :reviewStatus="reviewStatus"
            @update:reviewStatus="onReviewStatusUpdate" @page-change="onPageChange" @publish="publishAd" @reject="rejectAd" />
    </main>
</template>

<script setup lang="ts">
import Swal from "sweetalert2";
import { useGetAllSponsoredAdsRequests } from '@/composables/sponsored-ads/fetchRequests';
import { useReviewAd } from '@/composables/sponsored-ads/reviewAd';

const { getAllSponsoredAdsRequests, ads, loading, pagination, queryObj, status, searchTerm, totalAds, advertType: adType, reviewStatus } = useGetAllSponsoredAdsRequests();
const { reviewAd, loading: loadingReview, payload, adId } = useReviewAd();

const props = defineProps<{
    advertType: string;
    adTypeLabel: string;
}>();

const comment = ref('')

adType.value = props.advertType;

const onSearchTermUpdate = (newSearchTerm: string) => {
    pagination.value.page = 1;
    searchTerm.value = newSearchTerm;
    getAllSponsoredAdsRequests();
};

const onSortKeyUpdate = (newSortKey: string) => {
    pagination.value.page = 1;
    queryObj.value.sortBy = newSortKey;
    getAllSponsoredAdsRequests();
};

const onSortOrderUpdate = (newSortOrder: string) => {
    pagination.value.page = 1;
    queryObj.value.orderBy = newSortOrder;
    getAllSponsoredAdsRequests();
};

const onReviewStatusUpdate = (newReviewStatus: string) => {
    pagination.value.page = 1;
    reviewStatus.value = newReviewStatus;
    getAllSponsoredAdsRequests();
};

const onPageChange = (page: number) => {
    pagination.value.page = page
    getAllSponsoredAdsRequests()
}

const rejectAd = async (id: string) => {
    const result = await Swal.fire({
        title: "Reason for rejection?",
        text: "You won't be able to revert this!",
        input: "text",
        inputPlaceholder: "Enter your reason",
    })
    if (result.value) {
        comment.value = result.value;
        adId.value = id;
        payload.value = {
            reviewStatus: "rejected",
            comment: comment.value
        }
        await reviewAd();
        getAllSponsoredAdsRequests();
    } else {
        Swal.fire("Cancelled", "Action was cancelled", "info");
    }
}

const publishAd = async (id: string) => {
    adId.value = id;
    payload.value = {
        reviewStatus: "approved",
        comment: comment.value
    }
    await reviewAd();
    getAllSponsoredAdsRequests();
}

onMounted(async () => {
    status.value = false;
    adType.value = props.advertType;
    await getAllSponsoredAdsRequests();
});
</script>
