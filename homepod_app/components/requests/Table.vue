<template>
    <div class="flow-root mt-8">
        <div class="flex flex-col gap-3 my-5 sm:flex-row sm:items-center sm:justify-between sm:gap-24">
            <div class="relative flex items-center flex-1 border-gray-300 rounded h-9">
                <input type="text" :value="compSearchTerm" placeholder="Search"
                    class="flex-1 px-5 text-sm rounded focus:outline-none" @input="onSearch" />
                <svg class="absolute right-3" width="21px" height="21px" viewBox="0 0 24 24" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M14.9536 14.9458L21 21M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z"
                        stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                </svg>
            </div>

            <div class="flex flex-col gap-2 gap-x-2 sm:flex-row sm:items-center">
                <div class="space-x-2">
                    <label for="sortBy" class="text-sm font-medium text-gray-700">Sort By:</label>
                    <select id="sortBy" :value="compSortKey" @change="onSortKeyChange"
                        class="h-10 text-sm border-gray-300 rounded">
                        <option value="clientName">Client Name</option>
                        <option value="status">Status</option>
                        <option value="startDate">Start Date</option>
                        <option value="endDate">End Date</option>
                        <option value="createdAt">Date Created</option>
                    </select>
                </div>

                <div class="space-x-2">
                    <label for="status" class="text-sm font-medium text-gray-700">Order by:</label>
                    <select id="status" :value="compReviewStatus" @change="onReviewStatusChange"
                        class="h-10 text-sm border-gray-300 rounded">
                        <option value="">All</option>
                        <option value="pending">Pending</option>
                        <option value="rejected">Rejected</option>
                        <option value="approved">Approved</option>
                    </select>
                </div>

                <div class="space-x-2">
                    <label for="orderBy" class="text-sm font-medium text-gray-700">Order by:</label>
                    <select id="orderBy" :value="compSortOrder" @change="onSortOrderChange"
                        class="h-10 text-sm border-gray-300 rounded">
                        <option value="asc">Ascending Order</option>
                        <option value="desc">Descending Order</option>
                    </select>
                </div>
            </div>
        </div>

        <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div v-if="ads.length && !loading">
                <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                    <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 sm:rounded-lg">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-3 py-3.5 pl-4 text-left text-sm font-semibold text-gray-900">S/N</th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Client Name
                                    </th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">File Upload
                                    </th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Ads Link</th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Amount Paid
                                    </th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Start date
                                    </th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">End date</th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Action</th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Review Status
                                    </th>
                                    <th class="py-3.5 text-left text-sm font-semibold text-gray-900">Comment</th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Status</th>
                                    <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Date</th>
                                    <th class="relative py-3.5 pl-3 pr-4 sm:pr-6">
                                        <span class="sr-only">Edit</span>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="(ad, idx) in ads" :key="ad.id || idx" @click="onRowClick(ad)"
                                    class="cursor-pointer hover:bg-gray-100">
                                    <td class="text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-4">
                                        {{ (pagination.page - 1) * pagination.perPage + (idx + 1) }}
                                    </td>
                                    <td
                                        class="py-4 pl-4 pr-3 text-sm font-medium text-gray-900 whitespace-nowrap sm:pl-6">
                                        {{ ad.clientName ?? 'Nil' }}
                                    </td>
                                    <td class="px-3 py-4 pl-6 text-sm text-gray-500 whitespace-nowrap pr-9">
                                        <img v-if="ad.image" :src="ad.image"
                                            class="cursor-pointer rounded h-10 w-[300px]" alt="" />
                                        <span v-else>Nil</span>
                                    </td>
                                    <td class="px-3 py-4 text-sm text-gray-500 whitespace-nowrap">
                                        <a target="__blank" v-if="ad.link" :href="ad.link"
                                            class="text-green-600 underline">
                                            {{ ad.link.length > 30 ? ad.link.slice(0, 30) + '.....' : ad.link.slice(0,
                                                25) }}
                                        </a>
                                        <span v-else>Nil</span>
                                    </td>
                                    <td class="py-4 pl-5 pr-12 text-sm text-gray-500 whitespace-nowrap">
                                        {{ ad.amountPaid ? formatCurrency(ad.amountPaid) : 'Nil' }}
                                    </td>
                                    <td class="px-3 py-4 text-sm text-gray-500 whitespace-nowrap">
                                        <span v-if="ad.startDate">{{ formatDate(ad.startDate) }}</span>
                                        <span v-else>Nil</span>
                                    </td>
                                    <td class="px-3 py-4 text-sm text-gray-500 whitespace-nowrap">
                                        <span v-if="ad.endDate">{{ formatDate(ad.endDate) }}</span>
                                        <span v-else>Nil</span>
                                    </td>
                                    <td
                                        class="relative py-4 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6">
                                        <div class="flex items-center gap-x-3">
                                            <button @click.stop="$emit('publish', ad.id)"
                                                class="px-3 py-1 text-green-600 border border-black rounded-md">
                                                Publish
                                            </button>
                                            <button @click.stop="$emit('reject', ad.id)"
                                                class="px-3 py-1 text-red-600 border border-black rounded-md">
                                                Reject
                                            </button>
                                        </div>
                                    </td>
                                    <td class="py-4 pl-5 pr-12 text-sm text-gray-500 capitalize whitespace-nowrap">
                                        <span v-if="ad.reviewStatus">{{ ad.reviewStatus }}</span>
                                        <span v-else>Nil</span>
                                    </td>
                                    <td class="px-1" @click.stop="onCommentClick(ad.comment)">
                                        <img src="@/assets/icons/comment.svg" alt="" />
                                    </td>
                                    <td class="px-3 py-4 pr-5 text-sm text-gray-500 whitespace-nowrap">
                                        <span v-if="ad.status">{{ ad.status }}</span>
                                        <span v-else>Nil</span>
                                    </td>
                                    <td class="px-3 py-4 pr-5 text-sm text-gray-500 whitespace-nowrap">
                                        <span v-if="ad.createdAt">{{ formatShortDateTime(ad.createdAt) }}</span>
                                        <span v-else>Nil</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <CoreEmptyState v-if="ads.length === 0 && !loading" title="No Ads available" desc="" />

            <div v-if="loading" class="h-32 rounded bg-slate-100"></div>

            <CorePagination :total="pagination.total" :page="pagination.page" :perPage="pagination.perPage"
                :pages="pagination.pages" @page-changed="handlePageChange" />
        </div>

        <RequestsModal v-if="modalState.type === 'ad'" @close="closeModal">
            <template #title>{{ modalState.content.clientName ?? '-' }}</template>
            <template #content>
                <div v-if="modalState.content.image" class="w-full my-4 h-28">
                    <img :src="modalState.content.image" alt=""
                        class="object-cover object-center w-full rounded-lg has-animation h-28" />
                </div>
                <p>
                    <strong>Ads Link: </strong>
                    <a v-if="modalState.content.link" :href="modalState.content.link" target="_blank"
                        class="text-blue-600 underline">{{ modalState.content.link }}</a>
                    <span v-else>Nil</span>
                </p>
            </template>
        </RequestsModal>

        <RequestsModal v-if="modalState.type === 'comment'" @close="closeModal">
            <template #title>Comment</template>
            <template #content>
                <p class="whitespace-pre-wrap">{{ modalState.content }}</p>
            </template>
        </RequestsModal>

    </div>
</template>

<script setup lang="ts">
import { formatShortDateTime } from '@/composables/core/dateUtils'
import { ref, watch } from 'vue'

const props = defineProps({
    ads: {
        type: Array,
        required: true,
    },
    loading: {
        type: Boolean,
        required: true,
    },
    pagination: {
        type: Object,
        required: true,
    },
    searchTerm: {
        type: String,
        default: '',
    },
    sortKey: {
        type: String,
        default: 'clientName',
    },
    sortOrder: {
        type: String,
        default: 'asc',
    },
    reviewStatus: {
        type: String
    }
})

const emits = defineEmits([
    'update:searchTerm',
    'update:sortKey',
    'update:sortOrder',
    'update:reviewStatus',
    'page-change',
    'publish',
    'reject',
])

const compSearchTerm = ref(props.searchTerm)
const compSortKey = ref(props.sortKey)
const compSortOrder = ref(props.sortOrder)
const compReviewStatus = ref(props.reviewStatus)


watch(
    () => props.searchTerm,
    (val) => { compSearchTerm.value = val }
)
watch(
    () => props.sortKey,
    (val) => { compSortKey.value = val }
)
watch(
    () => props.sortOrder,
    (val) => { compSortOrder.value = val }
)
watch(
    () => props.reviewStatus,
    (val) => { compReviewStatus.value = val }
)

const onSearch = (event: Event) => {
    const target = event.target as HTMLInputElement
    compSearchTerm.value = target.value
    emits('update:searchTerm', target.value)
}

const onSortKeyChange = (event: Event) => {
    const target = event.target as HTMLSelectElement
    compSortKey.value = target.value
    emits('update:sortKey', target.value)
    emits('page-change', 1)
}

const onSortOrderChange = (event: Event) => {
    const target = event.target as HTMLSelectElement
    compSortOrder.value = target.value
    emits('update:sortOrder', target.value)
    emits('page-change', 1)
}

const onReviewStatusChange = (event: Event) => {
    const target = event.target as HTMLSelectElement
    compReviewStatus.value = target.value
    emits('update:reviewStatus', target.value)
    emits('page-change', 1)
}

const handlePageChange = (page: number) => {
    emits('page-change', page)
}

const modalState = ref<{ type: 'ad' | 'comment' | null; content: any }>({
    type: null,
    content: null,
});

const openModal = (type: 'ad' | 'comment', content: any) => {
    modalState.value = { type, content }
};

const closeModal = () => {
    modalState.value = { type: null, content: null }
};

const onRowClick = (ad: any) => {
    openModal('ad', ad)
};

const onCommentClick = (comment: string | null) => {
    openModal('comment', comment || 'No comment available')
};

</script>
