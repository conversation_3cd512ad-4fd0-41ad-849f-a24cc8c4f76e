import { adsApiFactory } from "@/apiFactory/ads";

export const useGetAdStats = () => {
    const loadingStats = ref(false);
    const stats = ref({ approved: 0, rejected: 0, pending: 0 });
    const advertType = ref('banner_ads');
    const getAdStats = async () => {
        loadingStats.value = true;
        try {
            const response = await adsApiFactory.$_fetch_ad_stats(advertType.value);
            stats.value = response?.data;
            return response; 
        } catch (error: any) {
            useNuxtApp().$toast.error(error.message, {
                autoClose: 5000,
                dangerouslyHTMLString: true,
            });
            return error;
        } finally {
            loadingStats.value = false;
        }
    };
    return { getAdStats, stats, loadingStats, advertType };
};
