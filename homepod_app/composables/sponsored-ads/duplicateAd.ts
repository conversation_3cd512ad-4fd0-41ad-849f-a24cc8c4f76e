import { adsApiFactory } from "@/apiFactory/ads";
 
export const useDuplicateAd = () => {
    const loading = ref(false);
    const payload = ref({
        advertId: ""
    })
    const duplicateAd = async () => {
        try {
            const response = await adsApiFactory.$_duplicate_ad(payload.value);
            useNuxtApp().$toast.success('Ad duplicated successfully!', {
                autoClose: 5000,
                dangerouslyHTMLString: true,
              });
            return response
        } catch (error : any) {
            useNuxtApp().$toast.error(error.message, {
                autoClose: 5000,
                dangerouslyHTMLString: true,
            });
            return error;
        } finally {
            loading.value = false;
        }
    }
    return {duplicateAd, duplicating:loading, payload};
}