{"name": "loud-mouth", "version": "1.0.0", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"prebuild": "sudo rm -rf dist", "prestart": "yarn migration", "build": "nest build", "postbuild": "cp -r src/app/notification/channels/email/templates dist/app/notification/channels/email/ && cp -r 'src/database/certs/Microsoft RSA Root Certificate Authority 2017.crt' dist/database/certs/", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "cross-env NODE_ENV=test jest --forceExit --detectOpenHandles --runInBand", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "cross-env NODE_ENV=test yarn db:refresh && cross-env NODE_ENV=test jest --config ./test/jest-e2e.json --runInBand ", "format:watch": "onchange 'src/**/*.ts' -- prettier --config .prettierrc 'src/**/*.ts' --write {{changed}}", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js -d src/database/cli-datasource.ts", "migration": "yarn typeorm migration:run", "migration:generate": "ts-node src/scripts/terminal/generate-migration", "migration:create": "yarn ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli.js migration:create", "migration:revert": "yarn typeorm migration:revert", "db:refresh": "yarn typeorm schema:drop && yarn migration", "seed": "ts-node -r tsconfig-paths/register src/seed.ts", "preseed": "yarn migration", "script": "ts-node -r tsconfig-paths/register src/scripts/app.ts", "test:coinbase": "ts-node -r tsconfig-paths/register src/scripts/test-coinbase-integration.ts", "migrate:logger": "node scripts/migrate-to-pino.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.216.0", "@aws-sdk/lib-storage": "^3.552.0", "@aws-sdk/s3-request-presigner": "^3.370.0", "@azure-rest/ai-content-safety": "^1.0.0", "@azure/communication-email": "^1.0.0", "@azure/identity": "latest", "@azure/storage-blob": "^12.18.0", "@base-org/account": "^2.1.0", "@base-org/account-ui": "^1.0.1", "@coinbase/cdp-sdk": "^1.36.0", "@coinbase/wallet-sdk": "^4.3.7", "@keyv/redis": "^4.2.0", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^11.1.3", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.3", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^11.0.0", "@nestjs/mapped-types": "^2.1.0", "@nestjs/microservices": "^11.1.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.3", "@nestjs/platform-socket.io": "^11.1.3", "@nestjs/schedule": "^6.0.0", "@nestjs/typeorm": "^11.0.0", "@nestjs/websockets": "^11.1.3", "@ntegral/nestjs-sentry": "^4.0.0", "@opentelemetry/exporter-jaeger": "^1.21.0", "@opentelemetry/exporter-trace-otlp-http": "^0.48.0", "@opentelemetry/instrumentation-express": "^0.35.0", "@opentelemetry/instrumentation-http": "^0.48.0", "@opentelemetry/instrumentation-nestjs-core": "^0.34.0", "@opentelemetry/resources": "^1.21.0", "@opentelemetry/sdk-node": "^0.48.0", "@opentelemetry/sdk-trace-base": "^1.21.0", "@opentelemetry/semantic-conventions": "^1.21.0", "@sendgrid/mail": "^8.1.5", "@sentry/node": "^7.79.0", "@socket.io/redis-adapter": "^8.3.0", "@supercharge/promise-pool": "^3.1.0", "@types/compression": "^1.7.5", "@types/crypto-js": "^4.2.2", "@types/csurf": "^1.11.2", "@types/geoip-lite": "^1.4.4", "@types/google-libphonenumber": "^7.4.23", "@types/jsonwebtoken": "^9.0.10", "@types/lodash": "^4.14.185", "@types/nodemailer": "^6.4.6", "bull": "^4.8.5", "cache-manager": "^6.4.0", "cache-manager-redis-store": "^3.0.1", "cacheable": "^1.8.8", "cheerio": "^1.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "compression": "^1.7.4", "cron-parser": "^5.2.0", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dotenv": "^10.0.0", "firebase-admin": "^11.11.0", "geoip-lite": "^1.4.10", "google-libphonenumber": "^3.2.30", "heic-convert": "^2.1.0", "helmet": "^4.6.0", "ioredis": "^5.4.1", "jsonwebtoken": "^9.0.2", "juice": "^11.0.1", "lodash": "^4.17.21", "moment": "^2.29.4", "nanoid": "^3.0.0", "nestjs-pino": "^4.4.0", "nestjs-rate-limiter": "^3.1.0", "nodemailer": "^6.7.8", "openai": "latest", "passport": "^0.6.0", "passport-jwt": "^4.0.0", "passport-local": "^1.0.0", "pg": "^8.8.0", "pino": "^9.7.0", "pino-http": "^10.5.0", "pino-pretty": "^13.0.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.2", "rimraf": "^3.0.2", "rxjs": "^7.8.1", "seamailer-nodejs": "^1.1.7", "sharp": "^0.33.5", "siwe": "^3.0.0", "socket.io": "^4.8.1", "template7": "^1.4.2", "typeorm": "^0.3.25", "typeorm-transactional": "^0.5.0", "viem": "^2.37.4"}, "devDependencies": {"@faker-js/faker": "^7.6.0", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.3", "@types/bull": "^3.15.9", "@types/express": "^5.0.3", "@types/jest": "^27.0.1", "@types/mime": "3.0.4", "@types/multer": "^2.0.0", "@types/node": "^24.0.3", "@types/passport-jwt": "^3.0.6", "@types/passport-local": "^1.0.34", "@types/supertest": "^2.0.11", "@typescript-eslint/eslint-plugin": "^4.28.2", "@typescript-eslint/parser": "^4.28.2", "eslint": "^7.30.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "jest": "^27.0.6", "prettier": "^2.3.2", "supertest": "^6.1.3", "ts-jest": "^27.0.3", "ts-loader": "^9.2.3", "ts-node": "^10.0.0", "tsconfig-paths": "^3.10.1", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "engines": {"node": ">=22.0.0", "npm": ">=10.0.0"}}