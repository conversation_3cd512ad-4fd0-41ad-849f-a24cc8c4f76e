import { Injectable, Logger, BadRequestException } from '@nestjs/common';
import { CdpClient } from '@coinbase/cdp-sdk';
import { UserService } from '../user/user.service';
import { User } from '../user/entities/user.entity';
import { CDP_API_KEY_ID, CDP_API_KEY_SECRET, CDP_WALLET_SECRET, CDP_NETWORK } from '../../config/env.config';

@Injectable()
export class BaseSubaccountService {
  private readonly logger = new Logger(BaseSubaccountService.name);
  private readonly cdp: CdpClient;

  constructor(private readonly userService: UserService) {
    // Initialize Coinbase CDP client with environment variables
    this.cdp = new CdpClient({
      apiKeyId: CDP_API_KEY_ID,
      apiKeySecret: CDP_API_KEY_SECRET,
      walletSecret: CDP_WALLET_SECRET,
    });
  }

  async createSubaccountForUser(user: User): Promise<string> {
    try {
      this.logger.log(`Creating subaccount for user ${user.id}`);

      // Create a new EVM account (subaccount) for the user using CDP SDK
      const account = await this.cdp.evm.createAccount();
      const subaccountAddress = account.address;

      // Update user with subaccount address
      await this.userService.updateProfile(user.id, {
        baseSubaccountAddress: subaccountAddress,
      });

      this.logger.log(`Created subaccount ${subaccountAddress} for user ${user.id}`);
      return subaccountAddress;
    } catch (error) {
      this.logger.error('Failed to create subaccount', error);
      throw new BadRequestException('Failed to create subaccount');
    }
  }

  async getSubaccountBalance(subaccountAddress: string): Promise<any> {
    try {
      return {
        address: subaccountAddress,
        balances: {
          ETH: '0',
          USDC: '0',
        },
      };
    } catch (error) {
      this.logger.error('Failed to get subaccount balance', error);
      throw new BadRequestException('Failed to get subaccount balance');
    }
  }

  async transferFromSubaccount(
    fromSubaccount: string,
    toAddress: string,
    amount: string,
    currency: string = 'ETH'
  ): Promise<string> {
    try {
      this.logger.log(`Transferring ${amount} ${currency} from ${fromSubaccount} to ${toAddress}`);

      // Use CDP SDK to send transaction from subaccount
      const transactionResult = await this.cdp.evm.sendTransaction({
        address: fromSubaccount as `0x${string}`,
        transaction: {
          to: toAddress as `0x${string}`,
          value: BigInt(amount), // Amount in wei for ETH
        },
        network: CDP_NETWORK as any,
      });

      this.logger.log(`Transfer completed with tx hash: ${transactionResult.transactionHash}`);
      return transactionResult.transactionHash;
    } catch (error) {
      this.logger.error('Failed to transfer from subaccount', error);
      throw new BadRequestException('Failed to transfer from subaccount');
    }
  }

  async fundSubaccount(
    subaccountAddress: string,
    amount: string,
    currency: string = 'ETH'
  ): Promise<string> {
    try {
      this.logger.log(`Funding subaccount ${subaccountAddress} with ${amount} ${currency}`);

      // Use CDP faucet to fund the subaccount with testnet tokens
      const faucetResponse = await this.cdp.evm.requestFaucet({
        address: subaccountAddress as `0x${string}`,
        network: CDP_NETWORK as any,
        token: currency.toLowerCase() as 'eth',
      });

      this.logger.log(`Funding completed with tx hash: ${faucetResponse.transactionHash}`);
      return faucetResponse.transactionHash;
    } catch (error) {
      this.logger.error('Failed to fund subaccount', error);
      throw new BadRequestException('Failed to fund subaccount');
    }
  }
}
