import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { CdpClient, parseEther } from '@coinbase/cdp-sdk';
import { UserService } from '../../user/user.service';
import { User } from '../../user/entities/user.entity';
import { LedgerService } from '../ledger/ledger.service';
import { CDP_API_KEY_ID, CDP_API_KEY_SECRET, CDP_WALLET_SECRET, CDP_NETWORK } from '../../../config/env.config';

export interface BasePaymentDto {
  fromUserId: string;
  toUserId: string;
  amount: string; // Amount in ETH
  currency?: string;
  description?: string;
}

export interface BasePaymentResult {
  transactionHash: string;
  fromAddress: string;
  toAddress: string;
  amount: string;
  currency: string;
  status: 'pending' | 'confirmed' | 'failed';
}

@Injectable()
export class BasePaymentService {
  private readonly logger = new Logger(BasePaymentService.name);
  private readonly cdp: CdpClient;

  constructor(
    private readonly userService: UserService,
    private readonly ledgerService: LedgerService,
  ) {
    // Initialize Coinbase CDP client with environment variables
    this.cdp = new CdpClient({
      apiKeyId: CDP_API_KEY_ID,
      apiKeySecret: CDP_API_KEY_SECRET,
      walletSecret: CDP_WALLET_SECRET,
    });
  }

  async sendPayment(paymentDto: BasePaymentDto): Promise<BasePaymentResult> {
    const { fromUserId, toUserId, amount, currency = 'ETH', description } = paymentDto;

    try {
      this.logger.log(`Processing Base payment: ${amount} ${currency} from ${fromUserId} to ${toUserId}`);

      // Get sender and receiver users
      const [fromUser, toUser] = await Promise.all([
        this.userService.findOne({ id: fromUserId }),
        this.userService.findOne({ id: toUserId }),
      ]);

      if (!fromUser || !toUser) {
        throw new NotFoundException('User not found');
      }

      if (!fromUser.baseSubaccountAddress || !toUser.baseSubaccountAddress) {
        throw new BadRequestException('Both users must have Base subaccounts');
      }

      // Convert amount to wei
      const amountInWei = parseEther(amount);

      // Check balance before sending
      const balance = await this.getAccountBalance(fromUser.baseSubaccountAddress);
      if (BigInt(balance.eth) < amountInWei) {
        throw new BadRequestException('Insufficient balance');
      }

      // Send transaction using CDP SDK
      const transactionResult = await this.cdp.evm.sendTransaction({
        address: fromUser.baseSubaccountAddress as `0x${string}`,
        transaction: {
          to: toUser.baseSubaccountAddress as `0x${string}`,
          value: amountInWei,
        },
        network: CDP_NETWORK as 'base-sepolia',
      });

      this.logger.log(`Base payment sent: ${transactionResult.transactionHash}`);

      // Record the transaction in our ledger system
      await this.recordPaymentInLedger(fromUser, toUser, amount, currency, transactionResult.transactionHash, description);

      return {
        transactionHash: transactionResult.transactionHash,
        fromAddress: fromUser.baseSubaccountAddress,
        toAddress: toUser.baseSubaccountAddress,
        amount,
        currency,
        status: 'pending',
      };
    } catch (error) {
      this.logger.error('Failed to send Base payment', error);
      throw new BadRequestException(`Payment failed: ${error.message}`);
    }
  }

  async getAccountBalance(_address: string): Promise<{ eth: string; usdc: string }> {
    try {
      // For now, return mock balance structure
      // In a real implementation, you would query the blockchain for actual balances
      return {
        eth: '0', // Balance in wei as string
        usdc: '0', // USDC balance in smallest unit
      };
    } catch (error) {
      this.logger.error('Failed to get account balance', error);
      throw new BadRequestException('Failed to get account balance');
    }
  }

  async getUserPaymentHistory(userId: string, _limit: number = 10): Promise<any[]> {
    try {
      const user = await this.userService.findOne({ id: userId });
      if (!user || !user.baseSubaccountAddress) {
        throw new NotFoundException('User or subaccount not found');
      }

      // This would typically query blockchain transaction history
      // For now, return empty array as placeholder
      return [];
    } catch (error) {
      this.logger.error('Failed to get payment history', error);
      throw new BadRequestException('Failed to get payment history');
    }
  }

  async fundUserAccount(userId: string, _amount: string): Promise<string> {
    try {
      const user = await this.userService.findOne({ id: userId });
      if (!user || !user.baseSubaccountAddress) {
        throw new NotFoundException('User or subaccount not found');
      }

      // Use CDP faucet to fund the account with testnet tokens
      const faucetResponse = await this.cdp.evm.requestFaucet({
        address: user.baseSubaccountAddress,
        network: CDP_NETWORK as 'base-sepolia',
        token: 'eth',
      });

      this.logger.log(`Funded account ${user.baseSubaccountAddress} with tx: ${faucetResponse.transactionHash}`);
      return faucetResponse.transactionHash;
    } catch (error) {
      this.logger.error('Failed to fund user account', error);
      throw new BadRequestException('Failed to fund account');
    }
  }

  private async recordPaymentInLedger(
    _fromUser: User,
    _toUser: User,
    _amount: string,
    _currency: string,
    transactionHash: string,
    _description?: string,
  ): Promise<void> {
    try {
      // This would integrate with your existing ledger system
      // to record the Base payment for accounting purposes
      this.logger.log(`Recording payment in ledger: ${transactionHash}`);
      
      // Example integration with existing ledger service
      // You would need to adapt this based on your ledger structure
      // await this.ledgerService.recordBasePayment({
      //   fromUserId: fromUser.id,
      //   toUserId: toUser.id,
      //   amount,
      //   currency,
      //   transactionHash,
      //   description,
      // });
    } catch (error) {
      this.logger.error('Failed to record payment in ledger', error);
      // Don't throw here as the blockchain transaction already succeeded
    }
  }
}
