import { Module } from '@nestjs/common';
import { BasePaymentService } from './base-payment.service';
import { BasePaymentController } from './base-payment.controller';
import { UserModule } from '../../user/user.module';
import { LedgerModule } from '../ledger/ledger.module';

@Module({
  imports: [UserModule, LedgerModule],
  controllers: [BasePaymentController],
  providers: [BasePaymentService],
  exports: [BasePaymentService],
})
export class BasePaymentModule {}
