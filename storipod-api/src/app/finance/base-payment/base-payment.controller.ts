import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  ParseUUIDPipe,
} from '@nestjs/common';
import { BasePaymentService, BasePaymentDto } from './base-payment.service';
import { JwtAuthGuard } from '../../authentication/guards/jwt-auth.guard';
import { IsString, IsNotEmpty, IsOptional, IsNumberString } from 'class-validator';

export class SendBasePaymentDto {
  @IsString()
  @IsNotEmpty()
  toUserId: string;

  @IsString()
  @IsNotEmpty()
  amount: string; // Amount in ETH

  @IsOptional()
  @IsString()
  currency?: string;

  @IsOptional()
  @IsString()
  description?: string;
}

export class FundAccountDto {
  @IsString()
  @IsNotEmpty()
  amount: string;
}

@Controller('finance/base-payment')
export class BasePaymentController {
  constructor(private readonly basePaymentService: BasePaymentService) {}

  @UseGuards(JwtAuthGuard)
  @Post('send')
  async sendPayment(@Body() dto: SendBasePaymentDto, @Request() req: any) {
    const fromUserId = req.user.id;
    
    const paymentDto: BasePaymentDto = {
      fromUserId,
      toUserId: dto.toUserId,
      amount: dto.amount,
      currency: dto.currency,
      description: dto.description,
    };

    return await this.basePaymentService.sendPayment(paymentDto);
  }

  @UseGuards(JwtAuthGuard)
  @Get('balance')
  async getBalance(@Request() req: any) {
    const user = req.user;
    if (!user.baseSubaccountAddress) {
      return { error: 'No Base subaccount found' };
    }

    return await this.basePaymentService.getAccountBalance(user.baseSubaccountAddress);
  }

  @UseGuards(JwtAuthGuard)
  @Get('history')
  async getPaymentHistory(
    @Request() req: any,
    @Query('limit') limit: string = '10',
  ) {
    const userId = req.user.id;
    return await this.basePaymentService.getUserPaymentHistory(userId, parseInt(limit));
  }

  @UseGuards(JwtAuthGuard)
  @Post('fund')
  async fundAccount(@Body() dto: FundAccountDto, @Request() req: any) {
    const userId = req.user.id;
    return {
      transactionHash: await this.basePaymentService.fundUserAccount(userId, dto.amount),
    };
  }

  @UseGuards(JwtAuthGuard)
  @Get('user/:userId/balance')
  async getUserBalance(@Param('userId', ParseUUIDPipe) userId: string) {
    // This endpoint could be used by admins to check any user's balance
    // You might want to add admin authorization here
    const user = await this.basePaymentService['userService'].findOne({ id: userId });
    if (!user || !user.baseSubaccountAddress) {
      return { error: 'User or subaccount not found' };
    }

    return await this.basePaymentService.getAccountBalance(user.baseSubaccountAddress);
  }
}
