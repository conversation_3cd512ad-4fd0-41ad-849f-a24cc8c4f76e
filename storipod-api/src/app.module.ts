import { HttpException, Module } from '@nestjs/common';
import { APP_FILTER, APP_INTERCEPTOR } from '@nestjs/core';
import { BullModule } from '@nestjs/bull';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { SentryInterceptor, SentryModule } from '@ntegral/nestjs-sentry';
import { DatabaseModule } from './database/database.module';
import { ExceptionsFilter } from './lib/exceptions-filter.lib';
import { LoggerModule } from './lib/pinoLogger/logger.module';
import { PinoHttpInterceptor } from './lib/pinoLogger/pino-http.interceptor';
import {
  cacheTTL,
  datasource,
  envIsDev,
  redisCache,
  redisQueue,
} from './config/env.config';
import { AuthenticationModule } from './app/authentication/authentication.module';
import { UserModule } from './app/user/user.module';
import { FollowsModule } from './app/follows/follows.module';
import { CategoryModule } from './app/category/category.module';
import { StoryModule } from './app/story/story.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { StorySlideModule } from './app/story-slide/story-slide.module';
import { ReactionsModule } from './app/reactions/reactions.module';
import { OtpModule } from './app/otp/otp.module';
import { FileModule } from './app/file/file.module';
import { SignedUrlModule } from './app/file/signed-url/signed-url.module';
import { CoverImageModule } from './app/cover-image/cover-image.module';
import { ReportModule } from './app/report/report.module';
import { BlockedUserModule } from './app/blocked-user/blocked-user.module';
import { UserTermsStatusModule } from './app/user-terms-status/user-terms-status.module';
import { PushDeviceModule } from './app/push-device/push-device.module';
import { FirebaseAdminModule } from './gateway/firebase/firebase.module';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { UserFeedbackModule } from './app/user-feedback/user-feedback.module';
import { UserConfigModule } from './app/user-config/user-config.module';
import { SponsoredAdModule } from './app/sponsored-ad/sponsored-ad.module';
import { TagsModule } from './app/tag/tag.module';
import { EngagementModule } from './app/engagement/engagement.module';
import { NotificationModule } from './app/notification/notification.module';
import { CommentReactionModule } from './app/comment-reaction/comment-reaction.module';
import { CommentModule } from './app/comment/comment.module';
import { SearchModule } from './app/search/search.module';
import { FinanceModule } from './app/finance/finance.module';
import { BasePaymentModule } from './app/finance/base-payment/base-payment.module';
import { SecurityQuestionModule } from './app/security-question/security-question.module';
import { UtilityModule } from './app/utility/utility.module';
import { BankDetailsModule } from './app/finance/bank-details/bank-detail.module';
import { CardDetailsModule } from './app/finance/card-details/card-details.module';
import { CacheModule } from '@nestjs/cache-manager';
import { createKeyv } from '@keyv/redis';
import { Keyv } from 'keyv';
import { CacheableMemory } from 'cacheable';
import { CacheHelperModule } from './common/helpers/cache.helper';
import { MessagingModule } from './app/messaging/messaging.module';
import { UserEventsModule } from './app/user/events/user-event.module';
import { OriginalsModule } from './app/originals/originals.module';
import { ScheduleModule } from '@nestjs/schedule';
import { CampaignCronJobService } from './app/campaign/campaign.cronjob.service';
import { CampaignModule } from './app/campaign/campaign.module';
import { PromotionModule } from './app/promotion/promotion.module';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    ConfigModule.forRoot({
      isGlobal: true,
      ignoreEnvFile: false,
      envFilePath: '.env',
    }),
    LoggerModule,
    DatabaseModule.forRoot({
      config: {
        datasource,
      },
    }),
    BullModule.forRoot({
      url: redisQueue,
    }),
    SentryModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (cfg: ConfigService) => ({
        dsn: cfg.get('SENTRY_DSN'),
        debug: true,
        environment: cfg.get('SENTRY_ENV'),
      }),
      inject: [ConfigService],
    }),
    CacheModule.register({
      isGlobal: true,
      stores: [
        createKeyv(redisCache, {
          namespace: envIsDev ? 'storipodcache' : 'storipodcache_prod',
        }),
        new Keyv({
          store: new CacheableMemory({ ttl: cacheTTL, lruSize: 5000 }),
        }),
      ],
    }),
    CacheHelperModule.forRoot(),
    NotificationModule.register(),
    EventEmitterModule.forRoot({ global: true }),
    FirebaseAdminModule.register(),
    EngagementModule.register(),
    AuthenticationModule,
    StoryModule,
    StorySlideModule,
    UserModule,
    CategoryModule,
    FollowsModule,
    ReactionsModule,
    OtpModule,
    FileModule,
    SignedUrlModule,
    CoverImageModule,
    ReportModule,
    BlockedUserModule,
    UserTermsStatusModule,
    PushDeviceModule,
    UserFeedbackModule,
    UserConfigModule,
    SponsoredAdModule,
    TagsModule,
    CommentReactionModule,
    CommentModule,
    SearchModule,
    FinanceModule,
    BasePaymentModule,
    SecurityQuestionModule,
    UtilityModule,
    BankDetailsModule,
    CardDetailsModule,
    MessagingModule,
    UserEventsModule,
    OriginalsModule,
    CampaignModule,
    PromotionModule,
  ],
  controllers: [AppController],
  providers: [
    { provide: APP_FILTER, useClass: ExceptionsFilter },
    {
      provide: APP_INTERCEPTOR,
      useFactory: () =>
        new SentryInterceptor({
          filters: [
            {
              type: HttpException,
              filter: (exception: HttpException) => 500 > exception.getStatus(), // Only report 500 errors
            },
          ],
        }),
    },
    {
      provide: APP_INTERCEPTOR,
      useFactory: (configService: ConfigService) =>
        new PinoHttpInterceptor(configService),
      inject: [ConfigService],
    },
    CampaignCronJobService,
    AppService,
  ],
})
export class AppModule {}
