import * as dotenv from 'dotenv';
dotenv.config();

type IEnv = {
  NODE_ENV: string;
  DB_PASSWORD: string;
  DB_USERNAME: string;
  DB_NAME: string;
  PORT: string;
  DB_PORT: string;
  DB_HOST: string;
  TOKEN_SECRET: string;
  REDIS_HOST: string;
  REDIS_PORT: string;
  REDIS_PASSWORD: string;
  REDIS_USER: string;
  SHA_512_HASH: string;
  JWT_SECRET: string;
  TOKEN_LENGTH: string;
  CRYPTOGRAPHIC_KEY: string;
  EMAIL_HOST: string;
  EMAIL_PORT: string;
  EMAIL_USER: string;
  EMAIL_PASSWORD: string;
  EMAIL_SECURE: string;
  DB_HOST_TEST: string;
  DB_PORT_TEST: string;
  DB_USERNAME_TEST: string;
  DB_PASSWORD_TEST: string;
  DB_NAME_TEST: string;
  AWS_REGION: string;
  WASABI_ACCESS_KEY: string;
  WASABI_SECRET_KEY: string;
  OPENAI_EMAIL: string;
  OPENAI_PASSWORD: string;
  GOOGLE_CLIENT_ID: string;
  GOOGLE_CLIENT_SECRET: string;
  APPLE_CLIENT_ID: string;
  LOGGING: string;
  PRIVATE_KEY_ID: string;
  PRIVATE_KEY: string;
  CLIENT_EMAIL: string;
  CLIENT_ID: string;
  AUTH_URI: string;
  TOKEN_URI: string;
  AUTH_PROVIDER_x509_CERT_URL: string;
  CLIENT_x509_CERT_URL: string;
  APP_ICON_URL: string;
  HONEYCOMB_API_KEY: string;
  ORIGINALS_AUTHORS: string;
  DB_SSL_CERT_ENABLED: string;
  DB_SSL_CERT_RELATIVE_PATH: string;
  AZURE_BLOB_ACCOUNT_NAME: string;
  AZURE_BLOB_ACCESS_KEY: string;
  AZURE_BLOB_CONTAINER_NAME: string;
  AZURE_COMM_SERV_CONNECTION_STRING: string;
  EMAIL_FROM: string;
  BULK_EMAIL_FROM: string;
  PAYSTACK_SECRET_KEY: string;
  PUSH_NOTIFICATION_API_KEY: string;
  PUSH_NOTIFICATION_ALLOWED_ORIGINS: string;
  PAYSTACK_PUBLIC_KEY: string;
  SEAMAILER_API_KEY: string;
  CACHE_TTL: string;
  UNSAFE_CATEGORY_THRESHOLD: string;
  CONTENT_SAFETY_KEY: string;
  CONTENT_SAFETY_ENDPOINT: string;
  TEST_RECIPIENTS: string;
  HOMEPOD_BASE_URL: string;
  EXTERNAL_CONTACT_LIST: string;
  SENDGRID_API_KEY: string;
  BUSHA_SECRET_KEY: string;
  BUSHA_BASE_URL: string;
  BUSHA_FLOAT_ACCOUNT_NUMBER: string;
  BUSHA_FLOAT_ACCOUNT_NAME: string;
  BUSHA_FLOAT_BANK_NAME: string;
  OPENAI_API_KEY: string;
  MAX_AMOUNT_PER_POD: string;
  EARNINGS_POOL_LOW_BALANCE_NOTIFICATION_EMAILS: string;
  STORIPOD_SMART_WALLET_ACCOUNT_NAME: string;
  PAYCREST_API_KEY: string;
  PAYCREST_API_SECRET: string;
  CB_BUSINESS_API_KEY_NAME: string;
  CB_BUSINESS_API_KEY_SECRET: string;
  USDC_TREASURY_WALLET_ADDRESS: string;
  USDC_TREASURY_WALLET_NETWORK: string;
  FERN_API_KEY: string;
  FERN_BASE_URL: string;
  CDP_API_KEY_ID: string;
  CDP_API_KEY_SECRET: string;
  CDP_WALLET_SECRET: string;
  CDP_NETWORK: string;
  [key: string]: string;
};

const env: IEnv = process.env as IEnv;
type EnvType =
  | 'local'
  | 'development'
  | 'staging'
  | 'test'
  | 'production'
  | 'pipeline';

export const appEnv: EnvType = (env.NODE_ENV as EnvType) || 'development';
export const envIsDev = appEnv !== 'production';
export const JWT_TOKEN_SECRET: string = env.TOKEN_SECRET;
export const PORT: string | number = env.PORT || 4700;
export const DB_PORT = parseInt(env.DB_PORT);
export const DB_USERNAME = env.DB_USERNAME;
export const DB_PASSWORD = env.DB_PASSWORD;
export const DB_NAME = env.DB_NAME;
export const DB_HOST = env.DB_HOST;
export const DEFAULT_PAGE_LIMIT = 50;
export const datasource =
  appEnv === 'pipeline' || appEnv === 'test'
    ? {
        host: env.DB_HOST_TEST,
        port: parseInt(env.DB_PORT_TEST),
        username: env.DB_USERNAME_TEST,
        password: env.DB_PASSWORD_TEST,
        database: env.DB_NAME_TEST,
      }
    : {
        host: DB_HOST,
        port: DB_PORT,
        username: process.env.DB_USERNAME,
        password: process.env.DB_PASSWORD,
        database: process.env.DB_NAME,
      };
export const redisEnv = {
  host: env.REDIS_HOST || '127.0.0.1',
  port: env.REDIS_PORT ? Number(env.REDIS_PORT) : 6380,
  password: env.REDIS_PASSWORD,
  user: env.REDIS_USER,
};
export const sha512Hash = env.SHA_512_HASH;
export type DatasourceConfig = typeof datasource;
export const jwtConstants = {
  secret: env.JWT_SECRET,
};
export const tokenLength = env.TOKEN_LENGTH || 4;
export const cryptoKey = env.CRYPTOGRAPHIC_KEY;
export const email = {
  host: env.EMAIL_HOST || 'smtp.ethereal.email',
  port: env.EMAIL_PORT || 587,
  secure: !!env.EMAIL_SECURE,
  user: env.EMAIL_USER,
  password: env.EMAIL_PASSWORD,
};
export const openAI = {
  openAIEmail: env.OPENAI_EMAIL,
  openAIPassword: env.OPENAI_PASSWORD,
};
export const AWS_REGION = env.AWS_REGION;
export const WASABI_ACCESS_KEY = env.WASABI_ACCESS_KEY;
export const WASABI_SECRET_KEY = env.WASABI_SECRET_KEY;
export const GOOGLE_CLIENT_ID = env.GOOGLE_CLIENT_ID;
export const GOOGLE_CLIENT_SECRET = env.GOOGLE_CLIENT_SECRET;
export const APPLE_CLIENT_ID = env.APPLE_CLIENT_ID;
export const PRESIGNED_URL_TTL = 60 * 10;
export const LOGGING = env.LOGGING
  ? env.LOGGING === 'true' || env.LOGGING === '1'
  : false;
export const PRIVATE_KEY_ID = env.PRIVATE_KEY_ID;
export const PRIVATE_KEY = env.PRIVATE_KEY;
export const CLIENT_EMAIL = env.CLIENT_EMAIL;
export const CLIENT_ID = env.CLIENT_ID;
export const AUTH_URI = env.AUTH_URI;
export const TOKEN_URI = env.TOKEN_URI;
export const AUTH_PROVIDER_x509_CERT_URL = env.AUTH_PROVIDER_x509_CERT_URL;
export const CLIENT_x509_CERT_URL = env.CLIENT_x509_CERT_URL;
export const QUEUE_DEFAULT_JOB_OPTIONS = {
  attempts: 5,
  backoff: {
    type: 'exponential',
    delay: 500,
  },
};
export const APP_ICON_URL =
  env.APP_ICON_URL ||
  'https://prodstoripodbucket.blob.core.windows.net/prod-storipod-assets/icons/storipod-icon.png';
export const HONEYCOMB_API_KEY = env.HONEYCOMB_API_KEY;
export const storipodOriginalsAuthors = env.ORIGINALS_AUTHORS;
export const dbSSLCertEnabled = env.DB_SSL_CERT_ENABLED;
export const dbSSLCertRelativePath = env.DB_SSL_CERT_RELATIVE_PATH;
export const azureBlobAccountName = env.AZURE_BLOB_ACCOUNT_NAME;
export const azureBlobAccessKey = env.AZURE_BLOB_ACCESS_KEY;
export const azureBlobContainerName = env.AZURE_BLOB_CONTAINER_NAME;
export const EMAIL_FROM = env.EMAIL_FROM;
export const BULK_EMAIL_FROM = env.BULK_EMAIL_FROM || '<EMAIL>';
export const BULK_EMAIL_FROM_NAME =
  env.BULK_EMAIL_FROM_NAME || 'Winibaby❤️ from Storipod';
export const AZURE_COMM_SERV_CONNECTION_STRING =
  env.AZURE_COMM_SERV_CONNECTION_STRING;
export const PUSH_NOTIFICATION_API_KEY = env.PUSH_NOTIFICATION_API_KEY;
export const PUSH_NOTIFICATION_ALLOWED_ORIGINS =
  env.PUSH_NOTIFICATION_ALLOWED_ORIGINS;
export const PAYSTACK_SECRET_KEY = env.PAYSTACK_SECRET_KEY;
export const PAYSTACK_PUBLIC_KEY = env.PAYSTACK_PUBLIC_KEY;
export const SEAMAILER_API_KEY = env.SEAMAILER_API_KEY;
export const cacheTTL = env.CACHE_TTL || 600000;
export const unsafeCategoryThreshold =
  Number(env.UNSAFE_CATEGORY_THRESHOLD) || 3;
export const contentSafetyKey = env.CONTENT_SAFETY_KEY;
export const contentSafetyEndpoint = env.CONTENT_SAFETY_ENDPOINT;
export const redisUrlWsAdapter = env.REDIS_URL_WS_ADAPTER;
export const AZURE_MANAGED_REDIS_URL = env.AZURE_MANAGED_REDIS_URL;
export const AZURE_MANAGED_REDIS_ACCESS_KEY =
  env.AZURE_MANAGED_REDIS_ACCESS_KEY;
export const TEST_RECIPIENTS = env.TEST_RECIPIENTS || '<EMAIL>';
export const HOMEPOD_BASE_URL = env.HOMEPOD_BASE_URL;
export const EXTERNAL_CONTACT_LIST =
  env.EXTERNAL_CONTACT_LIST || '<EMAIL>';
export const SENDGRID_API_KEY = env.SENDGRID_API_KEY;
export const BUSHA_SECRET_KEY = env.BUSHA_SECRET_KEY;
export const BUSHA_BASE_URL = env.BUSHA_BASE_URL || 'https://api.busha.co';
export const BUSHA_FLOAT_ACCOUNT_NUMBER = env.BUSHA_FLOAT_ACCOUNT_NUMBER;
export const BUSHA_FLOAT_ACCOUNT_NAME = env.BUSHA_FLOAT_ACCOUNT_NAME;
export const BUSHA_FLOAT_BANK_NAME = env.BUSHA_FLOAT_BANK_NAME;
export const OPENAI_API_KEY: string = env.OPENAI_API_KEY;
export const redisCache = env.REDIS_CACHE;
export const redisQueue = env.REDIS_QUEUE;
export const MAX_AMOUNT_PER_POD = Number(env.MAX_AMOUNT_PER_POD) || 2;
export const EARNINGS_POOL_LOW_BALANCE_NOTIFICATION_EMAILS =
  env.EARNINGS_POOL_LOW_BALANCE_NOTIFICATION_EMAILS || '';
export const LOWER_BALANCE_THRESHOLD =
  Number(env.LOWER_BALANCE_THRESHOLD) || 50000;
export const AdminPassKey = env.ADMIN_PASSKEY;
export const minpayoutNGN = Number(env.MIN_PAYOUT_NGN) || 500;
export const maxPayoutNGN = Number(env.MAX_PAYOUT_NGN) || 1000;
export const maxPayoutDailyNGN = Number(env.MAX_PAYOUT_DAILY_NGN) || 200000;
export const STORIPOD_SMART_WALLET_ACCOUNT_NAME =
  env.STORIPOD_SMART_WALLET_ACCOUNT_NAME;
export const PAYCREST_API_KEY = env.PAYCREST_API_KEY;
export const PAYCREST_API_SECRET = env.PAYCREST_API_SECRET;
export const CDP_PROJECT_ID = env.CDP_PROJECT_ID;
export const CDP_API_KEY_ID = env.CDP_API_KEY_ID;
export const CDP_API_KEY_SECRET = env.CDP_API_KEY_SECRET;
export const CDP_WALLET_SECRET = env.CDP_WALLET_SECRET;
export const CDP_NETWORK = env.CDP_NETWORK || 'base-sepolia';
export const CB_BUSINESS_API_KEY_NAME = env.CB_BUSINESS_API_KEY_NAME;
export const CB_BUSINESS_API_KEY_SECRET = env.CB_BUSINESS_API_KEY_SECRET;
export const USDC_TREASURY_WALLET_ADDRESS = env.USDC_TREASURY_WALLET_ADDRESS;
export const USDC_TREASURY_WALLET_NETWORK = env.USDC_TREASURY_WALLET_NETWORK;
export const FERN_API_KEY = env.FERN_API_KEY;
export const FERN_BASE_URL = env.FERN_BASE_URL;
export const STRIPE_SECRET_KEY = env.STRIPE_SECRET_KEY;
export const STRIPE_PUBLISHABLE_KEY = env.STRIPE_PUBLISHABLE_KEY;
