#!/usr/bin/env ts-node

/**
 * Test script for Coinbase CDP integration
 * 
 * This script tests the basic functionality of the Coinbase CDP integration
 * without requiring a full database setup.
 */

import { CdpClient } from '@coinbase/cdp-sdk';
import * as dotenv from 'dotenv';

// Load environment variables
dotenv.config();

async function testCdpConnection() {
  console.log('🔧 Testing Coinbase CDP Integration...\n');

  // Check environment variables
  const apiKeyId = process.env.CDP_API_KEY_ID;
  const apiKeySecret = process.env.CDP_API_KEY_SECRET;
  const walletSecret = process.env.CDP_WALLET_SECRET;
  const network = process.env.CDP_NETWORK || 'base-sepolia';

  console.log('📋 Environment Check:');
  console.log(`  CDP_API_KEY_ID: ${apiKeyId ? '✅ Set' : '❌ Missing'}`);
  console.log(`  CDP_API_KEY_SECRET: ${apiKeySecret ? '✅ Set' : '❌ Missing'}`);
  console.log(`  CDP_WALLET_SECRET: ${walletSecret ? '✅ Set' : '❌ Missing'}`);
  console.log(`  CDP_NETWORK: ${network}\n`);

  if (!apiKeyId || !apiKeySecret || !walletSecret) {
    console.log('❌ Missing required environment variables. Please check your .env file.');
    console.log('   Required variables: CDP_API_KEY_ID, CDP_API_KEY_SECRET, CDP_WALLET_SECRET');
    process.exit(1);
  }

  try {
    // Initialize CDP client
    console.log('🚀 Initializing CDP Client...');
    const cdp = new CdpClient({
      apiKeyId,
      apiKeySecret,
      walletSecret,
      debugging: true,
    });

    console.log('✅ CDP Client initialized successfully\n');

    // Test account creation
    console.log('👤 Testing Account Creation...');
    const account = await cdp.evm.createAccount({
      name: `test-account-${Date.now()}`,
    });

    console.log(`✅ Account created successfully!`);
    console.log(`   Address: ${account.address}`);
    console.log(`   Account ID: ${account.accountId}\n`);

    // Test faucet (only works on testnet)
    if (network === 'base-sepolia' || network === 'ethereum-sepolia') {
      console.log('💰 Testing Faucet...');
      try {
        const faucetResult = await cdp.evm.requestFaucet({
          address: account.address,
          network: network as 'base-sepolia',
          token: 'eth',
        });

        console.log(`✅ Faucet request successful!`);
        console.log(`   Transaction Hash: ${faucetResult.transactionHash}\n`);
      } catch (faucetError) {
        console.log(`⚠️  Faucet test failed (this is normal if rate limited):`);
        console.log(`   ${faucetError.message}\n`);
      }
    } else {
      console.log('⏭️  Skipping faucet test (not on testnet)\n');
    }

    // Test signature functionality
    console.log('✍️  Testing Message Signing...');
    try {
      const message = 'Hello from Storipod CDP integration test!';
      const signResult = await cdp.evm.signMessage({
        address: account.address,
        message,
      });

      console.log(`✅ Message signed successfully!`);
      console.log(`   Message: "${message}"`);
      console.log(`   Signature: ${signResult.signature}\n`);
    } catch (signError) {
      console.log(`⚠️  Message signing failed:`);
      console.log(`   ${signError.message}\n`);
    }

    console.log('🎉 All tests completed successfully!');
    console.log('\n📝 Next Steps:');
    console.log('   1. Set up your database and run migrations');
    console.log('   2. Start your NestJS application');
    console.log('   3. Test the authentication endpoints');
    console.log('   4. Implement frontend wallet connection');

  } catch (error) {
    console.error('❌ CDP Integration test failed:');
    console.error(`   ${error.message}`);
    
    if (error.message.includes('401') || error.message.includes('Unauthorized')) {
      console.log('\n💡 Troubleshooting:');
      console.log('   - Check that your CDP API keys are correct');
      console.log('   - Verify that your API keys have the necessary permissions');
      console.log('   - Make sure you\'re using the correct API key format');
    }
    
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testCdpConnection().catch(console.error);
}

export { testCdpConnection };
