DB_HOST=
DB_PORT=
DB_NAME=
DB_USERNAME=
DB_PASSWORD=
DB_HOST_TEST=
DB_PORT_TEST=
DB_NAME_TEST=
DB_USERNAME_TEST=
DB_PASSWORD_TEST=
AUTH_SECRET=
EMAIL_HOST=
EMAIL_PASSWORD=
EMAIL_USER=
EMAIL_PORT=
PORT=
EMAIL_SECURE=
REDIS_HOST=
REDIS_PORT=
REDIS_PASSWORD=
NODE_ENV=
JWT_SECRET=
SHA_512_HASH=
CRYPTOGRAPHIC_KEY=
OPENAI_EMAIL=
OPENAI_PASSWORD=
WASABI_ACCESS_KEY=
WASABI_SECRET_KEY=
AWS_REGION=
GOOGLE_CLIENT_ID=
GOOGLE_CLIENT_SECRET=
APPOPTICS_SERVICE_KEY=
PRIVATE_KEY_ID=
PRIVATE_KEY=
CLIENT_EMAIL=
CLIENT_ID=
AUTH_URI=
TOKEN_URI=
AUTH_PROVIDER_x509_CERT_URL=
CLIENT_x509_CERT_URL=
SENTRY_ENV=
SENTRY_DSN=
HONEYCOMB_API_KEY=
LOGGING=
OPENAI_API_KEY=
MAX_AMOUNT_PER_POD=2
EARNINGS_POOL_LOW_BALANCE_NOTIFICATION_EMAILS=

# Coinbase CDP SDK Configuration
CDP_API_KEY_ID=
CDP_API_KEY_SECRET=
CDP_WALLET_SECRET=
CDP_NETWORK=base-sepolia
