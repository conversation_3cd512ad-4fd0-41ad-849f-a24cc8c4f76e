# Coinbase CDP Integration for Storipod

This document outlines the Coinbase Developer Platform (CDP) integration that enables Base blockchain authentication and payments within the Storipod application.

## Overview

The integration provides:
- **Base Wallet Authentication**: Users can sign in using their Base wallet with SIWE (Sign-In with Ethereum)
- **Subaccount Management**: Each user gets their own Base subaccount for payments
- **Base Payments**: Users can send and receive payments using ETH and other tokens on Base
- **Faucet Integration**: Testnet funding for development and testing

## Architecture

### Core Components

1. **BaseAuthService** (`src/app/authentication/base-auth.service.ts`)
   - Handles SIWE message verification
   - Manages nonce generation and validation
   - Creates user accounts for Base wallet users

2. **BaseSubaccountService** (`src/app/authentication/base-subaccount.service.ts`)
   - Creates and manages user subaccounts using CDP SDK
   - Handles transfers between subaccounts
   - Provides faucet funding for testnet

3. **BasePaymentService** (`src/app/finance/base-payment/base-payment.service.ts`)
   - Manages payment flows between users
   - Integrates with existing ledger system
   - Provides balance checking and transaction history

4. **BasePaymentController** (`src/app/finance/base-payment/base-payment.controller.ts`)
   - REST API endpoints for Base payments
   - Authentication and authorization
   - Request validation and error handling

## Environment Configuration

Add the following environment variables to your `.env` file:

```bash
# Coinbase CDP SDK Configuration
CDP_API_KEY_ID=your_cdp_api_key_id
CDP_API_KEY_SECRET=your_cdp_api_key_secret
CDP_WALLET_SECRET=your_cdp_wallet_secret
CDP_NETWORK=base-sepolia
```

### Getting CDP API Keys

1. Visit the [Coinbase Developer Platform Portal](https://portal.cdp.coinbase.com/)
2. Create a new project or select an existing one
3. Navigate to API Keys section
4. Generate a new CDP Secret API Key
5. Download the JSON file containing your credentials
6. Extract the values and add them to your environment variables

## Database Schema Changes

The integration adds two new fields to the `user` table:

```sql
ALTER TABLE "user" 
ADD COLUMN "baseWalletAddress" character varying,
ADD COLUMN "baseSubaccountAddress" character varying;
```

Run the migration:
```bash
npm run migration
```

## API Endpoints

### Authentication Endpoints

#### Generate Nonce
```http
POST /auth/base/nonce
Content-Type: application/json

{
  "walletAddress": "******************************************"
}
```

#### Verify Signature and Login
```http
POST /auth/base/verify
Content-Type: application/json

{
  "walletAddress": "******************************************",
  "message": "Sign in to Storipod...",
  "signature": "0x..."
}
```

### Payment Endpoints

#### Send Payment
```http
POST /finance/base-payment/send
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "toUserId": "user-uuid",
  "amount": "0.001",
  "currency": "ETH",
  "description": "Payment for services"
}
```

#### Get Balance
```http
GET /finance/base-payment/balance
Authorization: Bearer <jwt_token>
```

#### Get Payment History
```http
GET /finance/base-payment/history?limit=20
Authorization: Bearer <jwt_token>
```

#### Fund Account (Testnet)
```http
POST /finance/base-payment/fund
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "amount": "0.1"
}
```

## Frontend Integration

### Sign In with Base Button

For React/Next.js applications:

```jsx
import { SignInWithBaseButton } from '@base-org/account-ui/react';

export function LoginPage() {
  const handleSignIn = async () => {
    // Implementation will depend on your wallet connection setup
    // This is a placeholder for the actual implementation
    console.log('Sign in with Base clicked');
  };

  return (
    <SignInWithBaseButton
      colorScheme="light"
      onClick={handleSignIn}
    />
  );
}
```

### Wallet Connection Flow

1. User clicks "Sign in with Base"
2. Frontend requests nonce from `/auth/base/nonce`
3. User signs the SIWE message with their wallet
4. Frontend sends signature to `/auth/base/verify`
5. Backend verifies signature and returns JWT token
6. User is authenticated and can make payments

## Development Setup

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Set Environment Variables**
   Copy the CDP credentials to your `.env` file

3. **Run Database Migration**
   ```bash
   npm run migration
   ```

4. **Start Development Server**
   ```bash
   npm run start:dev
   ```

## Testing

### Unit Tests
```bash
npm run test
```

### Integration Tests
```bash
npm run test:e2e
```

### Manual Testing with Testnet

1. Use Base Sepolia testnet for development
2. Fund accounts using the faucet endpoint
3. Test payment flows between test accounts
4. Verify transactions on Base Sepolia explorer

## Security Considerations

1. **API Key Management**: Store CDP API keys securely and never commit them to version control
2. **Signature Verification**: Always verify SIWE signatures server-side
3. **Nonce Management**: Implement proper nonce expiration and single-use validation
4. **Rate Limiting**: Implement rate limiting on authentication and payment endpoints
5. **Input Validation**: Validate all wallet addresses and transaction amounts
6. **Error Handling**: Don't expose sensitive information in error messages

## Production Deployment

1. **Network Configuration**: Change `CDP_NETWORK` to `base-mainnet` for production
2. **Real Funding**: Remove faucet endpoints and implement real funding mechanisms
3. **Monitoring**: Set up monitoring for transaction failures and API errors
4. **Backup**: Ensure proper backup of user wallet associations
5. **Compliance**: Ensure compliance with relevant financial regulations

## Troubleshooting

### Common Issues

1. **"Module not found" errors**: Ensure all CDP SDK dependencies are installed
2. **Database connection errors**: Verify database is running and credentials are correct
3. **Signature verification failures**: Check that the SIWE message format matches exactly
4. **Network errors**: Verify CDP API keys are valid and have proper permissions

### Debug Mode

Enable debugging in the CDP client:
```typescript
const cdp = new CdpClient({
  apiKeyId: CDP_API_KEY_ID,
  apiKeySecret: CDP_API_KEY_SECRET,
  walletSecret: CDP_WALLET_SECRET,
  debugging: true, // Enable for development
});
```

## Support

- [Coinbase Developer Platform Documentation](https://docs.cdp.coinbase.com/)
- [Base Network Documentation](https://docs.base.org/)
- [SIWE Specification](https://eips.ethereum.org/EIPS/eip-4361)
