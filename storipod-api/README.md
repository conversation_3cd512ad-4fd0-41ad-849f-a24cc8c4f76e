# Loud Mouth backend server

## Start Up Open Telemetry on Local Env.
### Do these first if you don't have docker installed
- install [docker](https://docs.docker.com/engine/install/)
- download [Docker Compose](https://docs.docker.com/compose/install/) specific to your Machine's OS. Be sure to mark Docker compose as an executable file

### Local Environment Observability
#### Start Jaeger container
run Jaeger container with `docker-compose up -d` to start the container in background

#### Open the Jaeger GUI on browser
after confirming that the Jaeger container is running, visit this route `http://localhost:16686/` on your browser and you should see the Jaeager window

### Develop/Production Observability
Log into [HoneyComb](https://ui.honeycomb.io) observability tool and you should be able to navigate all metrics on the dashboard
